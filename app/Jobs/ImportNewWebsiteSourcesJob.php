<?php

namespace App\Jobs;

use App\Enums\JobCategoryTag;
use App\Jobs\Traits\HasHighTimeout;
use App\Jobs\Traits\HasTags;
use App\Jobs\Traits\IsUnique;
use App\Models\Collection;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ImportNewWebsiteSourcesJob implements /*ShouldBeUnique,*/ ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use HasHighTimeout, HasTags/*, IsUnique*/;

    /**
     * Create a new job instance.
     */
    public function __construct(public Collection $collection, public bool $full = false)
    {
        $this->onQueue(env('QUEUE_DEFAULT', 'default'));
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::debug(class_basename(static::class));
        $this->collection->crawlWebsite($this->full);
    }

    /**
     * @return string[]
     */
    public function tags(): array
    {
        $full = bool_to_string($this->full);
        return array_merge(
            ["full:{$full}"],
            $this->generateTags([JobCategoryTag::IMPORT], $this->collection)
        );
    }

//    public function uniqueId(): string
//    {
//        return $this->generateUniqueKey($this->collection->id);
//    }
}
