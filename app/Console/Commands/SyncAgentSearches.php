<?php

namespace App\Console\Commands;

use App\Console\Commands\Traits\HasDateOffset;
use App\Models\AgentSearch;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SyncAgentSearches extends Command
{
    use HasDateOffset;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:agent-searches {days=3} {offsetDays?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Synchronize agent searches.';

    const UPSERT_CHUNK_SIZE = 1000;

    /**
     * Execute the console command.
     */
    public function handle(): void
    {

        $this->setDateRange();

        $this->newLine();
        $this->comment("Getting searches from {$this->startDate->toDateTimeString()} to {$this->endDate->toDateTimeString()} ...");

        $data = DB::connection(env('FRONTEND_DB_CONNECTION'))
            ->table('searches AS s')
            ->select(
                's.id',
                's.agent_id',
                's.agent_token_id',
                's.prompt_id AS agent_prompt_id',
                's.searched_at',
                DB::raw("DATE_TRUNC('second', now()::timestamp) AS synced_at")
            )
            ->where('searched_at', '>=', $this->startDate->toDateTimeString())
            ->where('searched_at', '<=', $this->endDate->toDateTimeString())
            ->get()
            ->toArray()
        ;
        $items = collect($data)->map(fn ($x) => (array) $x)->toArray();

        $numUpserted = 0;
        foreach (array_chunk($items, static::UPSERT_CHUNK_SIZE) as $itemsChunk) {
            $numUpserted += AgentSearch::upsert(
                $itemsChunk,
                ['id'],
                ['synced_at']
            );
        }

        $this->newLine();
        $this->info("{$numUpserted} searches upserted.");

        $this->newLine();

    }

}
