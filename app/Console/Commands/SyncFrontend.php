<?php
namespace App\Console\Commands;

use App\Models\Agent;
use App\Models\AgentCta;
use App\Models\AgentFrontend;
use App\Models\AgentIntegration;
use App\Models\AgentIntegrationPlatform;
use App\Models\AgentModel;
use App\Models\AgentModelProvider;
use App\Models\AgentQuestion;
use App\Models\AgentToken;
use App\Models\Category;
use App\Models\Team;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class SyncFrontend extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:frontend {models?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Synchronize all to frontend.';

    const UPSERT_CHUNK_SIZE = 1000;

    protected const MODELS = [
        Team::class => [],
        AgentModelProvider::class => [],
        AgentModel::class => [],
        Agent::class => [],
        AgentCta::class => [],
        AgentFrontend::class => [],
        AgentIntegration::class => [],
        AgentIntegrationPlatform::class => [],
        AgentQuestion::class => [],
        AgentToken::class   =>  [],
        Category::class => [],
    ];

    /**
     * Execute the console command.
     */
    public function handle(): void
    {

        $this->newLine();

        if (!empty($this->argument('models'))) {
            $passedModels = explode(',', $this->argument('models'));
            $modelsToSync = [];
            foreach (static::MODELS as $model => $excludeFields)
            {
                if (in_array(class_basename($model), $passedModels)) {
                    $modelsToSync[$model] = $excludeFields;
                }
            }
        } else {
            $modelsToSync = static::MODELS;
        }

//        DB::connection(env('FRONTEND_DB_CONNECTION'))->statement("SET session_replication_role = 'replica';");
        foreach ($modelsToSync as $model => $excludeFields) {
            $this->info(class_basename($model));
            $this->info(str_repeat('-', 16));
            $models = $model::all();
            $this->comment("Syncing {$models->count()} records ...");
            $models->each->syncToFrontend();
            $this->newLine();
        }
//        DB::connection(env('FRONTEND_DB_CONNECTION'))->statement("SET session_replication_role = 'origin';");

    }
}
