<?php

namespace App\Spiders;

use App\Enums\SourceType;
use App\Models\Model;
use App\Models\Source;
use Generator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use RoachPHP\Http\Response;
use Symfony\Component\DomCrawler\Crawler;

class WebsiteSpider extends Spider
{
    protected array $crawledUrls = [];

    protected ?string $baseUrl = null;

    public int $concurrency = 3;

    public int $requestDelay = 1;

    const SITEMAP_NAMESPACE = 'http://www.sitemaps.org/schemas/sitemap/0.9';

    public function parse(Response $response): Generator
    {

        Log::debug("WEBSITE {$this->context['model']->id}: Parsing: {$response->getUri()}");
        if (
            ($response->getStatus() >= 200) &&
            ($response->getStatus() < 400)
        ) {

            $url = $this->context['useProxy'] ? get_url_param($response->getUri(), 'url') : $this->sanitizeUrl($response->getUri());
            if (!empty($this->context['model']->source_url_restriction)) {
                $this->baseUrl = $this->context['model']->source_url_restriction;
            } else {
                $this->baseUrl = get_website_root($this->context['model']->url);
            }

            if (!in_array($url, $this->crawledUrls)) {

                $spider = false;
                $urlLanguage = $this->parseLanguage($response);

                if (empty($urlLanguage)) {
                    $urlLanguage = $this->context['model']->language;
                }

                if (str_ends_with($response->getUri(), '.xml')) {

                    // Try to get URLs generically
                    $links = $response->filterXPath('//loc')->each(function (Crawler $node, $i): string {
                        return $node->text();
                    });
                    $numLinks = count($links);

                    // If the sitemap can't be parsed, try forcing a namespace
                    if ($numLinks == 0) {
                        $response->registerNamespace('s', static::SITEMAP_NAMESPACE);
                        $links = $response->filterXPath('//s:loc')->each(function (Crawler $node, $i): string {
                            return $node->text();
                        });
                        $numLinks = count($links);
                    }

                    Log::debug("WEBSITE {$this->context['model']->id}: Total Sitemap URLs: {$numLinks}");
                    $links = array_slice($links, $this->context['resumeAt']);
                    Log::debug("WEBSITE {$this->context['model']->id}: Resume Sitemap At: {$this->context['resumeAt']}");

                } else {

                    $this->crawledUrls[] = $url;
                    $mime = parse_mime_type(
                        $response->getResponse()->getHeaderLine('Content-Type') ??
                        $response->getResponse()->getHeaderLine('content-type')
                    );
                    $this->addSource($url, $urlLanguage, $mime);
                    $spider = true;
                    $links = array_map(fn ($link) => $this->sanitizeUrl($link->getUri()), $response->filter('a')->links());

                }

                // Dedupe links
                $links = array_unique($links);

//                Log::debug(print_r($links, true));
                foreach ($links as $link) {

                    if (
                        !in_array($link, $this->crawledUrls) &&
                        filter_var($link, FILTER_VALIDATE_URL) &&
                        str_starts_with($link, $this->baseUrl)
                    ) {

                        Log::debug("CRAWL URL: {$link}");
                        $mime = get_remote_mime_type($link);
                        if (
                            (
                                !is_null($mime) &&
                                (
                                    is_html_mime($mime) ||
                                    is_media_mime($mime) ||
                                    is_doc_mime($mime)
                                )
                            ) ||
                            is_null($mime)
                        ) {

                            if ($spider && is_html_mime($mime)) {
                                $linkUrl = $this->context['useProxy'] ? get_proxied_url($link, true) : $link;
                                yield $this->request('GET', $linkUrl);
                            } else {
                                $this->addSource($link, $urlLanguage, $mime);
                            }

                        } else {
                            Log::debug("UNSUPPORTED URL: {$link}");
                        }

                    } else {
                        Log::debug("SKIP URL: {$link}");
                    }

                }

            }

        } else {
            Log::debug("Status: {$response->getStatus()}, skipping");
        }

    }

    protected function sanitizeUrl(string $url): string
    {
        if (str_contains($url, '?')) {
            $url = substr($url, 0, strpos($url, '?'));
        }
        if (str_ends_with($url, '/')) {
            $url = substr($url, 0, -1);
        }
        if (str_contains($url, '#')) {
            $url = substr($url, 0, strpos($url, '#'));
        }

        return $url;
    }

    protected function addSource(string $url, string $language, ?string $mime): Model|bool
    {

        if (!empty($url)) {

            if (is_html_mime($mime)) {
                Log::debug("ADD URL SOURCE: {$url}");
                $attrs = [
                    'type' => SourceType::URL,
                    'url_content_selector' => $this->context['model']->source_url_content_selector ?? null,
                    'url_content_strip' => $this->context['model']->source_url_content_strip ?? null,
                    'url_author_selector' => $this->context['model']->source_url_author_selector ?? null,
                    'url_published_selector' => $this->context['model']->source_url_published_selector ?? null,
                    'url_image_selector' => $this->context['model']->source_url_image_selector ?? null,
                    'url_render_js' => $this->context['model']->source_url_render_js,
                    'social_active' => $this->context['model']->auto_list_sources,
                    'agent_terms_accepted' => $this->context['model']->auto_index_sources,
                    'agent_active' => $this->context['model']->auto_index_sources,
                ];
            } else if (is_media_mime($mime)) {
                Log::debug("ADD MEDIA SOURCE: {$url}");
                $attrs = [
                    'type' => SourceType::MEDIA,
                    'media_url' => $url,
                ];
            } else if (is_doc_mime($mime)) {
                Log::debug("ADD ARTICLE SOURCE: {$url}");
                $attrs = [
                    'type' => SourceType::ARTICLE,
                    'media_url' => $url,
                ];
            }

            $attrs['name'] = Source::getPrefixedName();
            $attrs['language'] = $language;
            $attrs['url'] = $url;

            return $this->context['model']->addUniqueSource('url', $url, $attrs);

        } else {
            return false;
        }

    }
}
