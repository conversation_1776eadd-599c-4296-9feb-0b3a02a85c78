<?php

namespace App\Spiders;

use RoachPHP\Http\Response;
use RoachP<PERSON>\Spider\BasicSpider;
use League\HTMLToMarkdown\HtmlConverter;

abstract class Spider extends BasicSpider
{
    public int $concurrency = 5; // defaults

    public int $requestDelay = 1; // defaults

    protected function parseLanguage(Response $response): ?string
    {
        $code = $this->getSingleElementContents(
            $response,
            'html',
            'lang'
        );
        if ($code) {
            return parse_language_code($code);
        }

        return null;
    }

    protected function getSingleElementContents(Response $response, ?string $selector = null, ?string $attr = null, ?string $strip = null): ?string
    {
        $converter = new HtmlConverter(['strip_tags'=>true]);
        if (! empty($selector)) {
            $instances = $response->filter($selector);
            if ($instances->count() > 0) {
                $firstInstance = $instances->first();
                if (! is_null($attr)) {
                    $content = $firstInstance->attr($attr);
                } else {
                    $content = strip_links($firstInstance->html());
                    if (!empty($strip)) {
                        $content = str_replace($strip, '', $content);
                    }
                    $content = $converter->convert($content);
                }
                return trim(stripslashes(html_entity_decode($content, ENT_QUOTES, 'UTF-8')));
            }
        }

        return null;
    }
}
