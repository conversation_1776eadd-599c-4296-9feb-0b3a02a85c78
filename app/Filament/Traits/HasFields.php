<?php
namespace App\Filament\Traits;

use App\Enums\CollectionType;
use App\Enums\SourceContributionRole;
use App\Enums\SourceType;
use App\Models\Agent;
use App\Services\Formatter;
use App\Services\Gatekeeper;
use App\Services\Labeller;
use App\Services\LlmClient;
use App\Services\AmazonClient;
use Awcodes\Shout\Components\Shout;
use Closure;
use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Notifications\Notification;
use Filament\Support\Colors\Color;
use Filament\Support\Enums\Alignment;
use Filament\Support\Enums\IconPosition;
use Filament\Support\Enums\IconSize;
use FilamentTiptapEditor\Enums\TiptapOutput;
use FilamentTiptapEditor\TiptapEditor;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules\Unique;
use JaOcero\RadioDeck\Forms\Components\RadioDeck;
use Sawirricardo\FilamentNouislider\Forms\Nouislider;

trait HasFields
{

    protected static function getFormFieldName(bool $unique = true, bool $slug = true, string $label = 'Name'): Forms\Components\TextInput
    {

        $input = Forms\Components\TextInput::make('name')
            ->label($label)
            ->required()
            ->maxLength(250);

        if ($unique) {
            $input->unique(ignoreRecord: true, modifyRuleUsing: static::getUniqueByTeamAndTypeValidationClosure());
        }

        if ($slug) {
            $input
                ->live(onBlur: true)
                ->afterStateUpdated(function (Set $set, $state) {
                    $set('slug', Str::slug($state));
                })
            ;
        }

        return $input;

    }

    protected static function getFormFieldSlug(): Forms\Components\TextInput
    {
        return Forms\Components\TextInput::make('slug')
            ->required()
            ->maxLength(250)
            ->rules(['alpha_dash'])
            ->unique(ignoreRecord: true, modifyRuleUsing: static::getUniqueByTeamAndTypeValidationClosure());
    }

    protected static function getUniqueByTeamAndTypeValidationClosure(): \Closure
    {
        return function (Unique $rule, Get $get) {
            $rule->whereNull('deleted_at');
            if (! empty($get('team_id'))) {
                $rule->where('team_id', $get('team_id'));
            }
            if (! empty($get('type'))) {
                $rule->where('type', $get('type'));
            }
            return $rule;
        };
    }

    protected static function getFormFieldActive(
        string|Closure|HtmlString|null $hint = null,
        string $field = 'is_active',
        string|HtmlString $label = 'Active'
    ): Forms\Components\Toggle {
        return Forms\Components\Toggle::make($field)
            ->label($label)
            ->helperText($hint)
        ;
    }

    protected static function getFormFieldSocialActive($restrictPromotionClosure): Forms\Components\Toggle
    {
        $socialUrl = env('SOCIAL_URL');
        return static::getFormFieldActive(
            'This content must be submitted and approved to be listed.',
            'social_active',
            new HtmlString("Show Listing on <a href='{$socialUrl}' target='_blank' class='hover:underline text-gray-900 dark:text-white font-bold'>Apologist Social ↗</a>")
        )
            ->disabled($restrictPromotionClosure)
        ;
    }

    protected static function getFormFieldReferralUrl(): Forms\Components\TextInput
    {
        return Forms\Components\TextInput::make('referral_url')
            ->label('Learn More URL')
            ->maxLength(250)
            ->url()
            ->activeUrl()
            ->columnSpan(2);
    }

    protected static function getFormFieldImage(string $directory, string $field = 'image_path', string $label = 'Image'): Forms\Components\FileUpload
    {
        return Forms\Components\FileUpload::make($field)
            ->label($label)
            ->directory($directory)
            ->maxSize(4048)
            ->image();
        //            ->optimize('webp')
    }

    protected static function getFormFieldVideo(string $directory, string $field = 'image_path', string $label = 'Image'): Forms\Components\FileUpload
    {
        return Forms\Components\FileUpload::make($field)
            ->label($label)
            ->directory($directory)
            ->maxSize(10240)
            ->acceptedFileTypes([
                'video/*',
            ]);
    }

    protected static function getFormFieldEmail(): Forms\Components\TextInput
    {
        return Forms\Components\TextInput::make('email')
            ->label('Email')
            ->email()
            ->required()
            ->maxLength(250);
    }

    protected static function getFormFieldPassword(): Forms\Components\TextInput
    {
        return Forms\Components\TextInput::make('password')
            ->label('Password')
            ->password()
            ->dehydrateStateUsing(fn ($state) => Hash::make($state))
            ->dehydrated(fn ($state) => filled($state))
            ->required(fn (string $context): bool => $context === 'create')
            ->maxLength(250);
    }

    protected static function getFormFieldType(string $class): Forms\Components\Select
    {
        return static::getFormFieldSelect('type', $class::asOptions())
            ->enum($class);
    }

    protected static function getFormFieldSelect(string $field, array $options, ?string $label = null): Forms\Components\Select
    {
        return Forms\Components\Select::make($field)
            ->label($label ?? Labeller::mutate($field))
            ->required()
            ->options($options);
    }

    protected static function getFormFieldParent(array $conditions = []): Forms\Components\Select
    {
        return Forms\Components\Select::make('parent_id')
            ->label('Parent')
            ->relationship(
                'parent',
                'name',
                function (Builder $query, ?Model $record) use ($conditions) {
                    if ($record) {
                        $query->whereNot('id', $record->id);
                    }
                    if (! empty($conditions)) {
                        $query->where($conditions);
                    }
                }
            )
            ->searchable();
    }

    protected static function getFormFieldSources(?array $allowedSourceTypes = []): Forms\Components\CheckboxList
    {
        return Forms\Components\CheckboxList::make('sources')
            ->label('Sources')
            ->relationship(
                name: 'sources',
                titleAttribute: 'name',
                modifyQueryUsing: fn (Builder $query) => ! empty($allowedSourceTypes) ? $query->whereIn('sources.type', $allowedSourceTypes) : $query
            )
            ->searchable()
            ->columns(2)
            ->gridDirection('row')
            ->columnSpan(2);
    }

    protected static function getFormFieldCategoriesNotice(string $modelName): Shout
    {
        return Shout::make('categories_notice')
            ->content("Categories for this {$modelName} are set automatically based on Sources associated with it.")
            ->type('warning')
            ->columnSpan(2);
    }

    protected static function getFormFieldCategories(bool $displayOnly = false): Forms\Components\CheckboxList
    {
        return Forms\Components\CheckboxList::make('categories')
            ->label(false)
            ->relationship(
                name: 'categories',
                titleAttribute: 'name',
                modifyQueryUsing: function (Builder $query, ?Model $record) use ($displayOnly) {

                    $query
                        ->join('categories AS c2', 'categories.parent_id', '=', 'c2.id')
                        ->where('categories.is_active', true)
                        ->where('c2.is_active', true)
                        ->orderBy('categories.name', 'asc');

                    if ($record && $displayOnly) {
                        $query->whereIn('categories.id', $record->categories()->pluck('categories.id')->toArray());
                    }

                    return $query;

                },
            )
            ->getOptionLabelFromRecordUsing(fn (Model $record) => new HtmlString("
                {$record->name}
                <span class='ml-2 inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 uppercase ring-inset bg-gray-50 text-gray-600 ring-gray-600/10 dark:bg-gray-400/10 dark:text-gray-400 dark:ring-gray-400/20'>
                    {$record->parent->name}
                </span>
            "))
            ->searchable()
            ->columns(['sm' => 1, 'lg' => 2])
            ->gridDirection('row')
            ->columnSpan(2)
            ->saveRelationshipsUsing(function (Model $record, $state) {
                $record->saveCategoryRollup($state);
            })
            ->disabled($displayOnly)
            ->maxItems(fn () => Gatekeeper::userCan('categorize_unlimited', static::getModel()) ? 999 : env('MAX_CATEGORIES', 3));
    }

    protected static function getFormFieldOwner(bool $constrainToTeam = false): Forms\Components\Select
    {
        return Forms\Components\Select::make('user')
            ->label('Owner')
            ->required()
            ->default(auth()->user()->id)
            ->getOptionLabelFromRecordUsing(fn (Model $record) => "{$record->name} - {$record->email}")
            ->relationship(
                'owner',
                'name',
                function (Builder $query, ?Model $record) use ($constrainToTeam) {
                    if ($constrainToTeam || ($record && !Gatekeeper::userCanAdminister($record::class))) {
                        $query->whereExists(function($qry) {
                            $qry->select(DB::raw(1))
                                ->from('memberships')
                                ->whereColumn('model_id', 'users.id')
                                ->where('role_id', Gatekeeper::getManagerRoleId())
                                ->where('team_id', get_current_team_id())
                                ->whereNull('deleted_at')
                            ;
                        });
                    }
                }
            )
            ->searchable(['name', 'email'])
//            ->exists()
        ;
    }

    protected static function getFormFieldTeam(): Forms\Components\Select
    {
        return static::getFormFieldRelation('team_id', 'team', 'Team')
            ->required()
            ->default(get_current_team_id())
            ->exists('teams', 'id');
    }

    protected static function getFormFieldRelation(
        string $field,
        string $relation,
        ?string $label = null,
        string $optionField = 'name',
        Closure $optionHtmlClosure = null
    ): Forms\Components\Select
    {

        $field = Forms\Components\Select::make($field)
            ->label($label ?? Labeller::mutate($relation))
            ->relationship($relation, $optionField)
            ->searchable()
        ;

        if (!is_null($optionHtmlClosure)) {
            $field->allowHtml()
                ->getOptionLabelFromRecordUsing($optionHtmlClosure)
            ;
        }

        return $field;

    }

    protected static function getFormFieldMultiRelation(string $field, string $relation, ?string $label = null, string $optionField = 'name'): Forms\Components\Select
    {
        return static::getFormFieldRelation($field, $relation, $label, $optionField)
            ->multiple()
        ;
    }

    protected static function getFormFieldPastYear(string $field, string $label): Forms\Components\TextInput
    {
        return Forms\Components\TextInput::make($field)
            ->label($label)
            ->numeric()
            ->maxValue(Formatter::year(now()));
    }

    protected static function getFormFieldPastDate(string $field, string $label): Forms\Components\DatePicker
    {
        return Forms\Components\DatePicker::make($field)
            ->label($label)
            ->native(false)
            ->format('Y-m-d')
            ->displayFormat('Y-m-d')
            ->maxDate(now());
    }

    protected static function getFormFieldInlineRichText(string $field = 'description', string $label = 'Description'): Forms\Components\RichEditor
    {
        return Forms\Components\RichEditor::make($field)
            ->label($label)
            ->toolbarButtons([
                'bold',
                'italic',
                //                'link',
                'redo',
                'strike',
                'underline',
                'undo',
            ])
            ->extraInputAttributes(['class' => 'inline-rich-editor'])
            ->columnSpan(2);
    }

    protected static function getFormFieldBlockRichText(string $field = 'description', string $label = 'Description'): Forms\Components\RichEditor
    {
        return Forms\Components\RichEditor::make($field)
            ->label($label)
            ->disableToolbarButtons([
                'attachFiles',
                'h2',
                'h3',
                'codeBlock',
            ])
            ->columnSpan(2);
    }

    protected static function getFormFieldDescriptionActions(?string $sourceType): Forms\Components\Actions
    {
        return Forms\Components\Actions::make([

            Forms\Components\Actions\Action::make('ai_description')
                ->extraAttributes(['id' => btn_id('ai-description')])
                ->label('Rewrite Description')
                ->icon('heroicon-s-sparkles')
                ->outlined()
                ->action(function (Get $get, Set $set) {
                    $client = new LlmClient();
                    $newDescription = $client->rewrite($get('description'), 'summary');
                    if (! empty($newDescription)) {
                        $set('description', $newDescription);
                    } else {
                        Notification::make()
                            ->title('Error: could not rewrite description')
                            ->body('Please try again')
                            ->danger()
                            ->send();
                    }
                })
            ,

            Forms\Components\Actions\Action::make('revert_description')
                ->extraAttributes(['id' => btn_id('revert-description')])
                ->label('Get Description from Amazon.com')
                ->icon('heroicon-s-bars-arrow-down')
                ->outlined()
                ->action(function (Get $get, Set $set) {
                    $client = new AmazonClient(env('RAINFOREST_API_KEY'), env('AMAZON_ACCOUNT_ID'));
                    $bookId = $get('external_id');
                    if ($bookId) {
                        $cacheKey = "book.{$bookId}";
                        $book = Cache::rememberForever($cacheKey, function () use ($client, $bookId) {
                            return $client->getBook($bookId);
                        });
                        if (! empty($book['description'])) {
                            $set('description', $book['description']);
                        } else {
                            Notification::make()
                                ->title('Error: could not retrieve description')
                                ->body('Please try again')
                                ->danger()
                                ->send();
                        }
                    }
                })
                ->visible(fn ($record, Get $get) =>
                    !is_null($record) &&
                    ($sourceType == SourceType::BOOK->value) &&
                    !empty($get('external_id'))
                )
            ,

        ])
            ->columnSpan(2)
            ->alignment(Alignment::Center)
        ;
    }

    protected static function getFormFieldMediaRichText(string $field, string $label): Forms\Components\RichEditor
    {
        return Forms\Components\RichEditor::make($field)
            ->label($label)
            ->fileAttachmentsDisk('public')
            ->fileAttachmentsDirectory('attachments')
            ->fileAttachmentsVisibility('public')
            ->disableToolbarButtons([
                'h1',
            ]);
    }

    protected static function getFormFieldContributionRole(): Forms\Components\Select
    {
        return Forms\Components\Select::make('role')
            ->label('Role')
            ->required()
            ->options(SourceContributionRole::asOptions());
    }

    protected static function getFormFieldAvatar(string $directory, string $label = 'Image', string $field = 'avatar_path'): Forms\Components\FileUpload
    {
        return Forms\Components\FileUpload::make($field)
            ->label($label)
            ->image()
            ->imageEditor()
            ->avatar()
            ->circleCropper()
            ->directory($directory)
//            ->optimize('webp')
            ->maxSize(10240)
            ->deletable()
            ->extraAttributes(['class' => 'mx-auto']);
    }

    protected static function getFormFieldUrl(string $field = 'url', string $label = 'URL'): Forms\Components\TextInput
    {
        return Forms\Components\TextInput::make($field)
            ->label($label)
            ->url()
            ->activeUrl()
            ->maxLength(250);
    }

    protected static function getFormFieldWebUrlSelector(string $field, string $type): Forms\Components\TextInput
    {
        $typeTitle = ucwords($type);

        return Forms\Components\TextInput::make($field)
            ->label("URL {$typeTitle} Selector")
            ->maxLength(250)
            ->hintIcon(
                'heroicon-s-question-mark-circle',
                tooltip: "
                    A CSS selector that specifies where the {$type} is located at the URL(s).
                    Leave blank if unsure, and we'll do our best to figure it out automatically.
                "
            );
    }

    protected static function getFormFieldCollectionUrl(): Forms\Components\TextInput
    {
        return static::getFormFieldUrl()
            ->label(fn (Get $get) => Labeller::mutate($get('type')).' URL')
            ->visible(fn (Get $get) => (in_array($get('type'), [CollectionType::RSS->value, CollectionType::WEBSITE->value, CollectionType::PODCAST->value])))
            ->hintIcon(
                'heroicon-s-question-mark-circle',
                tooltip: fn (Get $get) => in_array($get('type'), [CollectionType::WEBSITE->value]) ? "Enter a website or sitemap URL." : null
            )
            ->required()
        ;
    }

    protected static function getFormFieldLanguage(): Select
    {
        return static::getFormFieldSelect(
            'language',
            config('agent.languages'),
            'Language'
        )
            ->required(true)
            ->default('en');
    }

    protected static function getFormFieldLanguages(?array $supportedLanguages = null): Select
    {
        $options = !is_null($supportedLanguages) ?
            Arr::only(Agent::getAvailableLanguages(true), $supportedLanguages) :
            Agent::getAvailableLanguages(true)
        ;
        return Select::make('languages')
            ->label('Supported Languages')
            ->options($options)
            ->multiple()
        ;
    }

    protected static function getFormFieldAgentLanguage(): Forms\Components\Select
    {
        return Forms\Components\Select::make('language')
            ->label('Language')
            ->options(config('agent.languages'))
            ->default('en');
    }

    protected static function getFormFieldSourceUrlRestriction(string $type): Forms\Components\TextInput
    {
        return static::getFormFieldUrl('source_url_restriction')
            ->label('Imported URL Prefix Restriction')
            ->hintIcon(
                'heroicon-s-question-mark-circle',
                tooltip: 'Only URLs that start with this prefix will be imported.'
            )
            ->visible(in_array($type, [CollectionType::RSS->value, CollectionType::WEBSITE->value]))
        ;
    }

    protected static function getFormFieldWeight(string $field = 'weight', string $label = 'Relevance'): Forms\Components\Select
    {
        return static::getFormFieldSelect(
            $field,
            config('agent.weights'),
            $label
        )
            ->required(false)
            ->default(1);
    }

    protected static function getFormFieldExternalId(string $label = 'External ID'): Forms\Components\TextInput
    {
        return Forms\Components\TextInput::make('external_id')
            ->label($label)
            ->maxLength(250);
    }

    protected static function getFormNoticePendingApproval(string $resourceName): Shout
    {
        return Shout::make('approval_pending')
            ->visible(fn ($record) => ! Gatekeeper::userCan('moderate', static::getModel()) &&
                ! is_null($record) &&
                $record->isPendingApproval()
            )
            ->content("You cannot make changes to this section while this {$resourceName} is pending approval.")
            ->type('warning')
            ->columnSpan(2)
        ;
    }

    protected static function getFormFieldsDateOrYear(string $field, string $label, array $excludeTypes = []): array
    {
        $dateTypes = [SourceType::ARTICLE->value, SourceType::URL->value, SourceType::MEDIA->value, SourceType::YOUTUBE->value, SourceType::EPISODE->value];

        return [
            static::getFormFieldPastYear("year_{$field}", "Year {$label}")
                ->visible(fn (Get $get) => ! in_array($get('type'), $excludeTypes) && ! in_array($get('type'), $dateTypes))
                ->live(onBlur: true)
                ->afterStateUpdated(function (?string $state, ?string $old, Set $set) use ($field) {
                    $set("{$field}_on", "{$state}-01-01");
                    $set("date_{$field}", "{$state}-01-01");
                }),
            static::getFormFieldPastDate("date_{$field}", "Date {$label}")
                ->visible(fn (Get $get) => ! in_array($get('type'), $excludeTypes) && in_array($get('type'), $dateTypes))
                ->live(onBlur: true)
                ->afterStateUpdated(function (?string $state, ?string $old, Set $set) use ($field) {
                    $set("{$field}_on", $state);
                    $set("year_{$field}", Formatter::year($state));
                }),
            Forms\Components\Hidden::make("{$field}_on")
                ->afterStateHydrated(function (?string $state, Set $set) use ($field) {
                    if (! empty($state)) {
                        $set("date_{$field}", $state);
                        $set("year_{$field}", Formatter::year($state));
                    }
                }),
        ];
    }

    protected static function getFormFieldTranslation(): Forms\Components\Select
    {
        return Forms\Components\Select::make('translation')
            ->label('Bible Translation')
            ->options(config('agent.translations'))
            ->default('esv');
    }

    protected static function getFormFieldFeatured(): Forms\Components\Select
    {
        $socialUrl = env('SOCIAL_URL');

        return Forms\Components\Select::make('featured_seq')
            ->label('Featured Order')
            ->options(range(1, 10));
    }

    protected static function getFormFieldHtmlEditor(string $field, string $label): TiptapEditor
    {
        return TiptapEditor::make($field)
            ->label($label)
            ->profile('custom') // default|simple|minimal|none|custom
            ->tools([
                'grid-builder', 'table', 'details',
                '|', 'heading', 'bullet-list', 'ordered-list', 'blockquote', 'hr',
                '|', 'align-left', 'align-center', 'align-right',
                '|', 'bold', 'italic', 'strike', 'underline',
                '|', 'link', 'media', 'oembed',
                '|', 'source',
            ]) // individual tools to use in the editor, overwrites profile
            ->disk('public') // optional, defaults to config setting
            ->directory('attachments') // optional, defaults to config setting
//            ->acceptedFileTypes(['array of file types']) // optional, defaults to config setting
//            ->maxSize('integer in KB') // optional, defaults to config setting
            ->output(TiptapOutput::Html) // optional, change the format for saved data, default is html
            ->disableFloatingMenus()
            ->disableBubbleMenus()
            ->maxContentWidth('5xl')
            ->extraInputAttributes(['style' => 'min-height: 12rem;']);
    }

    protected static function getFormFieldHtmlEditorBasic(string $field, string $label): TiptapEditor
    {
        return TiptapEditor::make($field)
            ->label($label)
            ->profile('custom') // default|simple|minimal|none|custom
            ->tools([
                'grid-builder',
                '|', 'heading', 'bullet-list', 'ordered-list', 'blockquote', 'hr',
                '|', 'align-left', 'align-center',
                '|', 'bold', 'italic', 'underline',
                '|', 'link', 'media', 'oembed',
                '|', 'source',
            ]) // individual tools to use in the editor, overwrites profile
            ->disk('public') // optional, defaults to config setting
            ->directory('attachments') // optional, defaults to config setting
//            ->acceptedFileTypes(['array of file types']) // optional, defaults to config setting
//            ->maxSize('integer in KB') // optional, defaults to config setting
            ->output(TiptapOutput::Html) // optional, change the format for saved data, default is html
            ->disableFloatingMenus()
            ->disableBubbleMenus()
            ->maxContentWidth('5xl')
            ->extraInputAttributes(['style' => 'min-height: 12rem;']);
    }

    protected static function getFormFieldSlider(
        string $field,
        string $label,
        string $tooltip,
        int|float $min,
        int|float|Closure $max,
        int|float $step
    ): Nouislider {
        return Nouislider::make($field)
            ->label($label)
            ->hintIcon(
                'heroicon-s-question-mark-circle',
                tooltip: $tooltip,
            )
            ->start([1])
            ->minValue($min)
            ->maxValue($max)
            ->step($step)
            ->tooltips()
            ->connect('lower')
        ;
    }

    protected static function getFormFieldTagline(): Forms\Components\TextInput
    {
        return Forms\Components\TextInput::make('tagline')
            ->label('Tagline')
            ->maxLength(100);
    }

    protected static function getFormFieldClassification(): Select
    {
        return static::getFormFieldRelation('classification_id', 'classification', 'Denominational Alignment');
    }

    protected static function getFormFieldKey(bool $unique = true, string $label = 'Key'): Forms\Components\TextInput
    {

        $input = Forms\Components\TextInput::make('key')
            ->label($label)
            ->required()
            ->maxLength(50);

        if ($unique) {
            $input->unique(ignoreRecord: true);
        }

        return $input;

    }

    protected static function getFormFieldProviderModel(): Forms\Components\TextInput
    {
        return TextInput::make('provider_model')
            ->label('Provider Model ID')
            ->required()
            ->maxLength(50)
            ->unique(ignoreRecord: true)
            ;
    }

    protected static function getFormFieldCardSelector(string $field, string $label, array $cards): RadioDeck
    {

        $options = array_combine(array_keys($cards), array_column($cards, 'label'));
        $descriptions = array_combine(array_keys($cards), array_column($cards, 'description'));
        $icons = array_combine(array_keys($cards), array_column($cards, 'icon'));

        return RadioDeck::make($field)
            ->label($label)
            ->options($options)
            ->descriptions($descriptions)
            ->icons($icons)
            ->required()
            ->iconSize(IconSize::Large)
            ->iconPosition(IconPosition::Before)
            ->alignment(Alignment::Center)
            ->color(Color::hex(env('AGENT_DEFAULT_PRIMARY_COLOR')))
            ->columns(2)
            ->columnSpan(2)
        ;

    }

    protected static function getFormFieldCacheTtl(string $field, string $label): Select
    {
        return Select::make($field)
            ->label($label)
            ->options(config('agent.cache_ttls'))
            ->default(3600);
    }

    protected static function getColumnBreakpoints(): array
    {
        return ['md'=>2];
    }

}
