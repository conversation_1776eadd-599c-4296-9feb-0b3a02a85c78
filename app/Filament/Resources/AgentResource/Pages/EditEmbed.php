<?php

namespace App\Filament\Resources\AgentResource\Pages;

use App\Filament\Resources\AgentResource;
use App\Filament\Traits\HasColumns;
use App\Filament\Traits\HasFilters;
use Filament\Forms\Components\ColorPicker;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Form;

class EditEmbed extends EditTemplate
{
    use HasColumns;
    use HasFilters;

    protected static ?string $navigationLabel = 'Embed';

    public function form(Form $form): Form
    {
return $form
            ->schema([
                Tabs::make('Tabs')
                    ->tabs([
                        static::getOptionsTab(),
//                        static::getEmbedTab(),
                    ])
                    ->persistTabInQueryString()
                    ->id('embed')
            ])
            ->disabled(AgentResource::getRestrictEditingClosure())
            ->columns(1)
        ;
    }

    protected static function getOptionsTab(): Tabs\Tab
    {
        $upgradeNotice = AgentResource::getUpgradeNotice();
        $canWhiteLabel = get_current_team()->getPlanOption('agent.white_label');
        return Tabs\Tab::make('Options')
            ->schema([

                $upgradeNotice->visible(!$canWhiteLabel),

                Fieldset::make('Layout')
                    ->relationship('frontend')
                    ->schema([
                        static::getFormFieldActive(
                            'Hide the logo, translation switcher, and language switcher when embedded.',
                            'embed_hide_header',
                            'Hide Header when Embedded'
                        ),
                    ])
                ,

                Fieldset::make('Beacon')
                    ->relationship('frontend')
                    ->schema([
                        ColorPicker::make('embed_icon_color')
                            ->label('Beacon Icon Background Color')
                        ,
                        static::getFormFieldImage('agents/embed_icons', 'embed_icon_path', 'Beacon Icon')
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'The icon that appears on the Beacon launcher.',
                            )
                        ,
                    ])
                ,

//                Fieldset::make('App')
//                    ->relationship('frontend')
//                    ->schema([
//                        ColorPicker::make('app_icon_color')
//                            ->label('App Icon Background Color')
//                        ,
//                        static::getFormFieldImage('agents/app_icons', 'app_icon_path', 'App Icon')
//                            ->hintIcon(
//                                'heroicon-s-question-mark-circle',
//                                tooltip: 'The icon that appears when saved to the home screen of a mobile device.',
//                            )
//                        ,
//                    ])
//                ,

            ])
            ->disabled(!$canWhiteLabel)
            ->columns(static::getColumnBreakpoints())
        ;
    }

}
