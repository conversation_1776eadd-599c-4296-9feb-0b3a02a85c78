<?php

namespace App\Filament\Resources\AgentResource\Pages;

use App\Enums\CollectionType;
use App\Filament\Resources\AgentResource;
use App\Filament\Traits\HasColumns;
use App\Filament\Traits\HasFilters;
use App\Models\Collection;
use App\Services\Labeller;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Illuminate\Support\Arr;
use Illuminate\Support\HtmlString;
use RalphJSmit\Filament\RecordFinder\Forms\Components\RecordFinder;

class EditUi extends EditTemplate
{
    use HasColumns;
    use HasFilters;

    protected static ?string $navigationLabel = 'UI Settings';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Tabs::make('Tabs')
                    ->tabs([
                        static::getProtectionTab(),
                        static::getMediaTab(),
                        static::getTrackingTab(),
                    ])
                    ->persistTabInQueryString()
                    ->id('ui')
            ])
            ->disabled(AgentResource::getRestrictEditingClosure())
            ->columns(1)
        ;
    }

    protected static function getProtectionTab(): Tabs\Tab
    {
        $upgradeNotice = AgentResource::getUpgradeNotice();
        $canPasswordProtect = get_current_team()->getPlanOption('agent.password_protection');
        return Tabs\Tab::make('Password Protection')
            ->schema([
                Grid::make(static::getColumnBreakpoints())
                    ->relationship('frontend')
                    ->schema([
                        $upgradeNotice->visible(!$canPasswordProtect),
                        static::getFormFieldActive(
                            'Require users to enter a username and password to access the Agent.',
                            'has_basic_auth',
                            'Enable Password Protection'
                        )
                            ->live()
                            ->columnSpan(2)
                        ,
                        TextInput::make('basic_auth_user')
                            ->label('Username')
                            ->maxLength(50)
                            ->required(fn (Get $get) => $get('has_basic_auth'))
                            ->disabled(fn (Get $get) => ! $get('has_basic_auth'))
                            ->dehydrated(fn (Get $get) => $get('has_basic_auth'))
                            ->columnSpan(['default' => 2, 'md' => 1])
                        ,
                        TextInput::make('basic_auth_password')
                            ->label('Password')
                            ->maxLength(250)
                            ->password()
                            ->autocomplete(false)
                            ->revealable()
                            ->required(fn (Get $get) => $get('has_basic_auth'))
                            ->disabled(fn (Get $get) => ! $get('has_basic_auth'))
                            ->dehydrated(fn (Get $get) => $get('has_basic_auth'))
                            ->columnSpan(['default' => 2, 'md' => 1])
                        ,
                    ])
                    ->disabled(!$canPasswordProtect)
                    ->columnSpan(2)
                ,
            ])
            ->columns(static::getColumnBreakpoints())
        ;
    }

    protected static function getMediaTab(): Tabs\Tab
    {
        $upgradeNotice = AgentResource::getUpgradeNotice();
        $canShowMedia = get_current_team()->getPlanOption('agent.media');
        $learnUrl = env('SEMANTIC_SEARCH_URL');
        return Tabs\Tab::make('Related Media')
            ->schema([
                Grid::make(static::getColumnBreakpoints())
                    ->relationship('frontend')
                    ->schema([
                        $upgradeNotice->visible(!$canShowMedia),
                        static::getFormFieldActive(
                            new HtmlString("
                                When enabled, each response will include related media (videos and/or podcasts).
                                You may select specific media Collections to draw on, or let any approved media Collection to be used.
                                Note that semantic search <strong>must</strong> be enabled to surface media in your Agent.
                                <a href='{$learnUrl}' target='_blank' class='text-primary-500 underline'>Learn more ↗</a>
                            "),
                            'show_media',
                            'Show Media'
                        )
                            ->live()
                        ,
                        static::getFormFieldMediaCollectionFinder()
                            ->disabled(fn (Get $get) => !$get('show_media'))
                            ->dehydrated(fn (Get $get) => $get('show_media'))
                        ,
                    ])
                ,
            ])
            ->disabled(!$canShowMedia)
            ->columns(static::getColumnBreakpoints())
        ;
    }

    protected static function getTrackingTab(): Tabs\Tab
    {
        return Tabs\Tab::make('Tracking')
            ->schema([
                Grid::make(static::getColumnBreakpoints())
                    ->relationship('frontend')
                    ->schema([
                        Textarea::make('custom_scripts')
                            ->label('Custom Scripts')
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'Here you can add analytics scripts and other custom Javascript.',
                            )
                            ->rows(10)
                            ->extraAttributes(['class'=>'font-mono'])
                            ->columnSpan(2)
                        ,
                    ])
                ,
            ])
            ->columns(static::getColumnBreakpoints())
        ;
    }

    protected static function getFormFieldMediaCollectionFinder(): RecordFinder
    {
        return static::getFormFieldRecordFinder(
            'mediaCollections',
            Collection::class,
            Collection::whereIn('type', ['channel', 'podcast']),
            fn () => [
                static::getTableColumnName(),
                static::getTableColumnType(),
            ],
            fn () => [
                static::getTableFilterMyTeam(),
                static::getTableFilterType(Arr::only(CollectionType::asOptions(), ['channel', 'podcast'])),
                static::getTableFilterLanguage(),
                static::getTableFilterRelation('categories'),
                static::getTableFilterRelation('classification', 'Denominational Alignment'),
            ],
            function (Collection $collection) {
                $type = Labeller::mutate($collection->type->value);
                return new HtmlString("<span class='font-bold'>{$collection->name}</span> [{$type}]");
            },
            false,
            'Media Collections',
            'Media added to selected Collections will automatically be included.',
            'Select Media Collections'
        );
    }

}
