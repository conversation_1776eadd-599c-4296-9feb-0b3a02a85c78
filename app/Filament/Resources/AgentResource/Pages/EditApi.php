<?php

namespace App\Filament\Resources\AgentResource\Pages;

use App\Filament\Resources\AgentResource;
use App\Models\Agent;
use App\Services\Gatekeeper;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Illuminate\Support\HtmlString;

class EditApi extends EditTemplate
{

    protected static ?string $navigationLabel = 'API';

    public static function canAccess(array $parameters = []): bool
    {
        return Gatekeeper::userCanAdminister(Agent::class);
    }

    public function form(Form $form): Form
    {
        $canConfigureApi = get_current_team()->getPlanOption('agent.api');
        $upgradeNotice = AgentResource::getUpgradeNotice()->visible(!$canConfigureApi);
        return $form
            ->schema([
                Tabs::make('Tabs')
                    ->tabs([
                        static::getFormatTab($canConfigureApi, $upgradeNotice),
                        static::getOptionsTab($canConfigureApi, $upgradeNotice),
                        static::getSearchTab($upgradeNotice),
                    ])
                    ->persistTabInQueryString()
                    ->id('api')
                ,
            ])
            ->disabled(AgentResource::getRestrictEditingClosure())
            ->columns(1)
        ;
    }

    protected static function getFormatTab(bool $canConfigureApi, $upgradeNotice): Tabs\Tab
    {

        $formats = [
            'raw' => [
                'label' => 'OpenAI Compatible Stream',
                'description' => 'Best for streaming use cases as a direct swap-in to replace OpenAI integrations. Use any OpenAI SDK.',
                'icon' => 'heroicon-s-cube-transparent',
            ],
            'text' => [
                'label' => 'Plain Text / Markdown',
                'description' => 'Best for use cases where the raw text is desired in order to process it or display it in a non-web context.',
                'icon' => 'heroicon-s-italic',
            ],
            'html' => [
                'label' => 'HTML',
                'description' => 'Best for directly displaying output on a web page. Any markdown output is automatically converted to HTML.',
                'icon' => 'heroicon-s-code-bracket',
            ],
            'json' => [
                'label' => 'JSON',
                'description' => 'Best for non-streaming use cases or where a structured response is required, such as platform integrations.',
                'icon' => 'heroicon-o-square-3-stack-3d',
            ],
        ];

        return Tabs\Tab::make('Format')
            ->schema([
                Grid::make(static::getColumnBreakpoints())
                    ->schema([
                        $upgradeNotice,
                        static::getFormFieldCardSelector('api_response_format', 'Default Response Format', $formats)
                            ->default('raw')
                        ,
                        static::getFormFieldActive(
                            '
                                Remove Markdown characters from plain text responses.
                                Useful for voice integrations or elsewhere that Markdown won\'t work well.
                            ',
                            'strip_markdown',
                            'Strip Markdown from Response Text'
                        ),
                    ])
                ,
            ])
            ->disabled(!$canConfigureApi)
            ->columns(static::getColumnBreakpoints())
        ;

    }

    protected static function getOptionsTab(bool $canConfigureApi, $upgradeNotice): Tabs\Tab
    {
        return Tabs\Tab::make('Options')
            ->schema([
                Grid::make(static::getColumnBreakpoints())
                    ->schema([
                        $upgradeNotice,
                        TextInput::make('max_memories')
                            ->label('Max Context Memories')
                            ->numeric()
                            ->step(1)
                            ->minValue(0)
                            ->maxValue(10)
                            ->default(env('AGENT_DEFAULT_MEMORIES'))
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'The maximum number of previous prompt/response exchanges to provide the model as context. Values 0 to 10.'
                            )
                        ,
                        static::getFormFieldActive(
                            '
                                Prevent API requests from overriding this Agent\'s system prompt.
                                Useful for integrations that require an undesired system prompt override.
                            ',
                            'lock_system_prompt',
                            'Lock System Prompt'
                        ),
                    ])
                ,
            ])
            ->disabled(!$canConfigureApi)
            ->columns(static::getColumnBreakpoints())
        ;
    }

    protected static function getSearchTab($upgradeNotice): Tabs\Tab
    {
        $canEnableSearch = get_current_team()->getPlanOption('agent.semantic_search');
        $learnUrl = env('SEMANTIC_SEARCH_URL');
        return Tabs\Tab::make('Semantic Search')
            ->schema([
                $upgradeNotice->visible(!$canEnableSearch),
                static::getFormFieldActive(
                    new HtmlString("
                        Enable semantic search via this Agent's API.
                        Note that semantic search <strong>must</strong> be enabled to surface media in your Agent.
                        <a href='{$learnUrl}' target='_blank' class='text-primary-500 underline'>Learn more ↗</a>
                    "),
                    'has_semantic_search',
                    'Enable Semantic Search'
                )
                    ->live()
                ,
                static::getFormFieldCacheTtl('search_cache_ttl', 'Cache Duration')
                    ->hintIcon(
                        'heroicon-s-question-mark-circle',
                        tooltip: 'Searches will be cached for the specified duration. This can be overridden via headers or URL params on individual requests.'
                    )
                    ->disabled(fn (Get $get) => !$get('has_semantic_search'))
                ,
            ])
            ->disabled(!$canEnableSearch)
            ->columns(static::getColumnBreakpoints())
        ;
    }

}
