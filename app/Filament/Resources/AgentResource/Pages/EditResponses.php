<?php

namespace App\Filament\Resources\AgentResource\Pages;

use App\Enums\AgentModelType;
use App\Enums\AgentReasoningEffort;
use App\Enums\AgentVerbosity;
use App\Enums\CollectionType;
use App\Enums\SourceType;
use App\Filament\Resources\AgentResource;
use App\Filament\Traits\HasColumns;
use App\Filament\Traits\HasFilters;
use App\Models\Agent;
use App\Models\AgentModel;
use App\Models\Category;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use App\Models\Collection;
use App\Models\Contributor;
use App\Models\Model;
use App\Models\Source;
use App\Models\Tag;
use App\Services\Gatekeeper;
use App\Services\Labeller;
use Awcodes\Shout\Components\Shout;
use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieTagsInput;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;
use RalphJSmit\Filament\RecordFinder\Forms\Components\RecordFinder;

class EditResponses extends EditTemplate
{
    use HasColumns;
    use HasFilters;

    protected static ?string $navigationLabel = 'Responses';

    protected static EloquentCollection $models;

    public function form(Form $form): Form
    {
        $team = get_current_team();
        $canSetInstructions = $team->getPlanOption('agent.instructions');
        return $form
            ->schema([
                Tabs::make('Tabs')
                    ->tabs([
                        static::getInstructionsTab($canSetInstructions),
                        static::getSourcesTab(),
                        static::getModelTab(),
                        static::getContextTab($canSetInstructions),
                        static::getParametersTab(),
                        static::getHierarchyTab($canSetInstructions),
                    ])
                    ->persistTabInQueryString()
                    ->id('responses')
                ,
            ])
            ->disabled(AgentResource::getRestrictEditingClosure())
            ->columns(1)
        ;
    }

    protected static function getInstructionsTab(bool $canSetInstructions): Tabs\Tab
    {
        $upgradeNotice = AgentResource::getUpgradeNotice();
        return Tabs\Tab::make('Instructions')
            ->schema([
                $upgradeNotice->visible(!$canSetInstructions),
                Shout::make('instructions_notice')
                    ->content('These are the Agent\'s baseline operating instructions on what persona to take on and how to respond to prompts.')
                    ->color('primary')
                    ->icon('heroicon-s-information-circle')
                    ->columnSpan(2)
                ,
                Textarea::make('model_system_prompt')
                    ->label(false)
                    ->default(env('AGENT_DEFAULT_PROMPT'))
                    ->rows(20)
                    ->columnSpan(2)
                ,
            ])
            ->disabled(!$canSetInstructions)
            ->columns(static::getColumnBreakpoints())
        ;
    }

    protected static function getSourcesTab(): Tabs\Tab
    {

        $upgradeNotice = AgentResource::getUpgradeNotice();
        $hasCustomCorpus = get_current_team()->has_custom_corpus;
        $canHaveCustomSources = get_current_team()->getPlanOption('agent.custom_sources');

        return Tabs\Tab::make('Knowledge')
            ->schema([

                $upgradeNotice->visible(!$canHaveCustomSources),

                Shout::make('sources_notice')
                    ->visible(!$hasCustomCorpus)
                    ->content(new HtmlString('
                        All approved content contributed by the community will be used if no constraints are added here.
                        Every effort is made to validate the authenticity and relevance of materials provided by 3rd party contributors.
                        We are not responsible for copyright infringement on the part of content contributors.
                    '))
                    ->color('primary')
                    ->icon('heroicon-s-information-circle')
                    ->columnSpan(2)
                ,
                Shout::make('custom_corpus_notice')
                    ->visible($hasCustomCorpus)
                    ->content(new HtmlString('
                        Your team is configured to use a custom corpus.
                        This Agent will therefore not have access to community sources.
                        You may only use sources that you upload to this team.
                        Contact us if you would like to revert back to using the community corpus.
                    '))
                    ->color('warning')
                    ->icon('heroicon-s-exclamation-triangle')
                    ->columnSpan(2)
                ,

                Forms\Components\Fieldset::make('Scope')
                    ->schema([
                        static::getFormFieldActive(
                            'Use all Sources contributed by your team, regardless of whether they are in the community corpus or not.',
                            'use_team_corpus',
                            'Use All Team Sources'
                        )
                            ->default($canHaveCustomSources)
                            ->disabled(!$canHaveCustomSources || $hasCustomCorpus)
                            ->live()
                        ,
                        static::getFormFieldActive(
                            'When enabled, team sources will be prioritized over community sources when referencing content. Responses will be slightly slower.',
                            'prioritize_team_corpus',
                            'Prioritize Team Sources'
                        )
                            ->disabled(fn (Get $get) => !$get('use_team_corpus'))
                        ,
                        static::getFormFieldActive(
                            'Do not use any community Sources as context, other than what is specifically selected below.',
                            'disable_community_corpus',
                            'Disable Community Sources'
                        )
                            ->disabled(!$canHaveCustomSources || $hasCustomCorpus)
                        ,
                    ])
                    ->columnSpan(2)
                ,

                Forms\Components\Fieldset::make('Sources')
                    ->schema([
                        static::getFormFieldCategoryFinder(),
                        static::getFormFieldCollectionFinder($hasCustomCorpus),
                        static::getFormFieldContributorFinder(),
                        static::getFormFieldSourceFinder($hasCustomCorpus),
                        static::getFormFieldClassification()
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'Only sources that match this denominational alignment will be included.',
                            )
                        ,
                        SpatieTagsInput::make('tags')
                            ->reorderable()
                            ->suggestions(fn () => Tag::query()->team()->pluck('name')->all())
                        ,
                    ])
                    ->columnSpan(2)
                ,

            ])
            ->disabled(!$canHaveCustomSources)
            ->columns(static::getColumnBreakpoints())
        ;

    }

    protected static function getModelTab(): Tabs\Tab
    {

        $team = get_current_team();
        if (Gatekeeper::isAdmin()) {
            $modelTypes = AgentModelType::values();
        } else {
            $modelTypes = $team->getPlanOption('agent.model_categories');
        }
        static::$models = AgentModel::whereIn('type', $modelTypes)->where('is_active', true)->orderBy('seq', 'ASC')->get();

        $modelOptions = [];
        foreach ($modelTypes as $type)
        {
            $typeLabel = Str::title($type);
            $modelOptions[$typeLabel] = [];
            $modelsOfType = static::$models->where('type', $type);
            foreach ($modelsOfType as $model)
            {
                $modelOptions[$typeLabel][$model->id] = "{$model->name} ({$model->num_credits})";
            }
        }

        $upgradeNotice = AgentResource::getUpgradeNotice();
        $canSetModel = $team->getPlanOption('agent.model_config');

        return Tabs\Tab::make('Model')
            ->schema([
                $upgradeNotice->visible(!$canSetModel),
                Shout::make('advanced_notice')
                    ->content('The options below can drastically change how the Agent responds. Make sure to thoroughly test changes to these parameters.')
                    ->color('warning')
                    ->icon('heroicon-o-exclamation-triangle')
                    ->columnSpan(2)
                ,
                Select::make('model_id')
                    ->label('Model (# credits)')
                    ->options($modelOptions)
                    ->afterStateUpdated(fn (string $state, Set $set) => $set('model_max_tokens', static::$models->find($state)->max_tokens))
                    ->hintIcon(
                        'heroicon-s-question-mark-circle',
                        tooltip: 'The underlying base model to use. The number of credits used per response is listed in parentheses after the model name.'
                    )
                    ->required()
                    ->default(env_int('AGENT_DEFAULT_MODEL_ID'))
                    ->live()
                    ->columnSpan(2)
                ,
            ])
            ->disabled(!$canSetModel)
            ->columns(static::getColumnBreakpoints())
        ;
    }

    protected static function getContextTab(bool $canSetInstructions): Tabs\Tab
    {
        $upgradeNotice = AgentResource::getUpgradeNotice();
        return Tabs\Tab::make('Context')
            ->schema([
                $upgradeNotice->visible(!$canSetInstructions),
                Shout::make('context_instructions_notice')
                    ->content('These are the Agent\'s instructions on how to incorporate context into its response using merge fields.')
                    ->color('primary')
                    ->icon('heroicon-s-information-circle')
                    ->columnSpan(2)
                ,
                Textarea::make('model_context_prompt')
                    ->label(false)
                    ->default(env('AGENT_CONTEXT_PROMPT'))
                    ->helperText(view('components.help.agent-context-prompt'))
                    ->maxLength(1000)
                    ->rows(10)
                    ->columnSpan(2)
                ,
            ])
            ->disabled(!$canSetInstructions)
            ->columns(static::getColumnBreakpoints())
        ;
    }

    protected static function getParametersTab(): Tabs\Tab
    {

        $upgradeNotice = AgentResource::getUpgradeNotice();
        $canSetAdvancedConfig = get_current_team()->getPlanOption('agent.advanced_config');

        return Tabs\Tab::make('Parameters')
            ->schema([
                $upgradeNotice->visible(!$canSetAdvancedConfig),
                Shout::make('advanced_notice')
                    ->content('Valid parameters for the selected model are shown below. These parameters can drastically change how the Agent responds. Make sure to thoroughly test changes to them.')
                    ->color('warning')
                    ->icon('heroicon-o-exclamation-triangle')
                    ->columnSpan(2)
                ,
                Select::make('model_reasoning_effort')
                    ->label('Reasoning Effort')
                    ->options(AgentReasoningEffort::asOptions())
                    ->default(AgentReasoningEffort::MEDIUM)
                    ->hintIcon(
                        'heroicon-s-question-mark-circle',
                        tooltip: 'This option is only available on select reasoning models.'
                    )
                    ->hidden(fn (Get $get) => !empty($get('model_id') && !static::$models->find($get('model_id'))->supports_reasoning_effort))
                ,
                Select::make('model_verbosity')
                    ->label('Verbosity')
                    ->options(AgentVerbosity::asOptions())
                    ->default(AgentVerbosity::MEDIUM)
                    ->hintIcon(
                        'heroicon-s-question-mark-circle',
                        tooltip: 'This option is only available on select models.'
                    )
                    ->hidden(fn (Get $get) => !empty($get('model_id') && !static::$models->find($get('model_id'))->supports_verbosity))
                ,
                static::getFormFieldSlider(
                    'model_temperature',
                    'Temperature',
                    'The amount of creativity (variability) allowed. Values from from 0.0 to 2.0. Some reasoning models don\'t support this.',
                    0,
                    2,
                    .05
                )
                    ->default(env_float('AGENT_DEFAULT_TEMPERATURE'))
                    ->hidden(fn (Get $get) => !$canSetAdvancedConfig || (!empty($get('model_id')) && !static::$models->find($get('model_id'))->supports_temperature))
                ,
                static::getFormFieldSlider(
                    'model_top_p',
                    'Top P',
                    'The probability cut-off of the token results to be considered. Lower values mean higher probability. Values from 0.0 to 1.0. Some reasoning models don\'t support this.',
                    0,
                    1,
                    .05
                )
                    ->default(env_float('AGENT_DEFAULT_TOPP'))
                    ->hidden(fn (Get $get) => !$canSetAdvancedConfig || (!empty($get('model_id')) && !static::$models->find($get('model_id'))->supports_top_p))
                ,
                static::getFormFieldSlider(
                    'model_frequency_penalty',
                    'Frequency Penalty',
                    'How much to penalize new tokens based on their existing frequency in the text so far, to reduce repetition. Values from -2.0 to 2.0. Some reasoning models don\'t support this.',
                    -2,
                    2,
                    .1
                )
                    ->default(0)
                    ->hidden(fn (Get $get) => !$canSetAdvancedConfig || (!empty($get('model_id')) && !static::$models->find($get('model_id'))->supports_frequency_penalty))
                ,
                static::getFormFieldSlider(
                    'model_presence_penalty',
                    'Presence Penalty',
                    'How much to penalize new tokens based on whether they appear in the text so far, to increase the likelihood of new topics being mentioned. Values from -2.0 to 2.0. Some reasoning models don\'t support this.',
                    -2,
                    2,
                    .1
                )
                    ->default(0)
                    ->hidden(fn (Get $get) => !$canSetAdvancedConfig || (!empty($get('model_id')) && !static::$models->find($get('model_id'))->supports_presence_penalty))
                ,
                static::getFormFieldSlider(
                    'model_max_tokens',
                    'Max Output Tokens',
                    'The maximum number of response tokens.',
                    1024,
                    fn (Get $get) => $get('model_id') ?
                        static::$models->find($get('model_id'))->max_tokens :
                        env_int('AGENT_DEFAULT_MAX_TOKENS')
                    ,
                    1024
                )
                    ->default(env_int('AGENT_DEFAULT_MAX_TOKENS'))
                    ->disabled(!$canSetAdvancedConfig)
                ,
                static::getFormFieldCacheTtl('completion_cache_ttl', 'Cache Duration')
                    ->hintIcon(
                        'heroicon-s-question-mark-circle',
                        tooltip: 'Responses will be cached for the specified duration. This can be overridden via headers or URL params on individual requests.'
                    )
                ,
            ])
            ->disabled(!$canSetAdvancedConfig)
            ->columns(static::getColumnBreakpoints())
        ;
    }

    protected static function getHierarchyTab(bool $canSetInstructions): Tabs\Tab
    {
        $upgradeNotice = AgentResource::getUpgradeNotice();
        return Tabs\Tab::make('Hierarchy')
            ->schema([
                $upgradeNotice->visible(!$canSetInstructions),
                Forms\Components\Select::make('parent')
                    ->label('Parent Agent')
                    ->relationship(
                        name: 'parent',
                        titleAttribute: 'name',
                        modifyQueryUsing: function (Builder $query, ?Agent $record) {
                            $query
                                ->approved()
                                ->where(function (Builder $qry) {
                                    return $qry->where('is_extensible', true)
                                        ->orWhere('is_template', true);
                                });
                            if ($record) {
                                $query->where('id', '!=', $record->id);
                            }
                        }
                    )
                    ->searchable()
                    ->helperText("
                        Automatically prepend the selected Agent's instructions.
                        Only your team's Agents and any 3rd party Agents that have opted in will appear here.
                    ")
                    ->disabled(!$canSetInstructions)
                ,
                static::getFormFieldActive(
                    new HtmlString("
                        This Agent's instructions can be prepended to any other Agent within your team.
                        Toggling this option will also allow 3rd party Agents to prepend this Agent's instructions.
                        Note that the Agent's instructions will not be revealed to 3rd parties unless the Agent is flagged as open source.
                    "),
                    'is_extensible',
                    'Allow Agent to be Extended'
                )
                    ->disabled(!$canSetInstructions)
                ,
            ])
            ->disabled(!$canSetInstructions)
            ->columns(static::getColumnBreakpoints())
        ;
    }

    protected static function getFormFieldSourceFinder(): RecordFinder
    {
        return static::getFormFieldRecordFinder(
            'sources',
            Source::class,
            Source::where('agent_active', true)->whereNotNull('agent_indexed_at'),
            fn () => [
                static::getTableColumnName(),
                static::getTableColumnType(),
                TextColumn::make('published_on')
                    ->label('Published')
                    ->date()
                    ->sortable()
                ,
            ],
            fn () => [
                static::getTableFilterMyTeam(),
                static::getTableFilterType(SourceType::asOptions()),
                static::getTableFilterLanguage(),
                static::getTableFilterRelation('categories'),
                static::getTableFilterRelation('collections')
                    ->getOptionLabelFromRecordUsing(function (Model $record) {
                        $type = Labeller::mutate($record->type->value);
                        return "{$record->name} [{$type}]";
                    }),
                static::getTableFilterContributors(),
                static::getTableFilterRelation('classification', 'Denominational Alignment'),
            ],
            function (Source $source) {
                $type = Labeller::mutate($source->type->value);
                return new HtmlString("<span class='font-bold'>{$source->name}</span> [{$type}]");
            },
            false,
            'Individual Sources',
            'Only the specific Sources selected will be included.'
        );
    }

    protected static function getFormFieldCollectionFinder(): RecordFinder
    {
        return static::getFormFieldRecordFinder(
            'collections',
            Collection::class,
            Collection::query(),
            fn () => [
                static::getTableColumnName(),
                static::getTableColumnType(),
            ],
            fn () => [
                static::getTableFilterMyTeam(),
                static::getTableFilterType(CollectionType::asOptions()),
                static::getTableFilterLanguage(),
                static::getTableFilterRelation('categories'),
                static::getTableFilterRelation('classification', 'Denominational Alignment'),
            ],
            function (Collection $collection) {
                $type = Labeller::mutate($collection->type->value);
                return new HtmlString("<span class='font-bold'>{$collection->name}</span> [{$type}]");
            },
        );
    }

    protected static function getFormFieldContributorFinder(): RecordFinder
    {
        return static::getFormFieldRecordFinder(
            'contributors',
            Contributor::class,
            Contributor::query(),
            fn () => [
                static::getTableColumnName(),
            ],
            fn () => [
                static::getTableFilterMyTeam(),
            ]
        );
    }

    protected static function getFormFieldCategoryFinder(): RecordFinder
    {
        return static::getFormFieldRecordFinder(
            'categories',
            Category::class,
            Category::active(),
            fn () => [
                static::getTableColumnName(),
                static::getTableColumnParent(),
            ],
            fn () => [
                static::getTableFilterMyTeam(),
            ],
            function (Category $category) {
                return new HtmlString(
                    "<span class='font-bold'>{$category->name}</span>".
                    ($category->parent ? " [{$category->parent->name}]" : '')
                );
            },
            true
        );
    }

}
