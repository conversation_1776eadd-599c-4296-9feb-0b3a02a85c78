<?php

namespace App\Filament\Resources\AgentResource\Pages;

use App\Models\Team;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Form;
use Filament\Forms\Get;

class EditAdmin extends EditTemplate
{

    protected static ?string $navigationLabel = 'Admin';

    public function form(Form $form): Form
    {
        return $form
            ->schema([

                Tabs::make('Tabs')
                    ->tabs([
//                        static::getFeaturesTab(),
                        static::getExtensibilityTab(),
                        static::getSettingsTab(),
                        static::getOwnershipTab(),
                    ])
                    ->persistTabInQueryString()
                    ->id('admin')
                ,
            ])
            ->columns(1)
        ;
    }

    protected static function getFeaturesTab(): Tabs\Tab
    {
        return Tabs\Tab::make('Features')
            ->schema([

            ])
            ->columns(static::getColumnBreakpoints())
        ;
    }

    protected static function getExtensibilityTab(): Tabs\Tab
    {
        return Tabs\Tab::make('Extensibility')
            ->schema([
                static::getFormFieldActive(
                    'Show this Agent in the worldview selector.',
                    'is_selectable',
                    'Selectable as Worldview'
                ),
                static::getFormFieldActive(
                    'Allow this Agent to be selected as a 1st party base template.',
                    'is_template',
                    'Selectable as Base Template'
                ),
            ])
            ->columns(static::getColumnBreakpoints())
        ;
    }

    protected static function getSettingsTab(): Tabs\Tab
    {
        return Tabs\Tab::make('Settings')
            ->schema([
                static::getFormFieldActive(
                    'Do not charge the team for use of this Agent.',
                    'is_nonbillable',
                    'Do Not Bill'
                )
                    ->default(fn (Get $get) => Team::find($get('team'))?->is_nonbillable),
                static::getFormFieldActive(
                    'Show debugging logs.',
                    'debug',
                    'Debug'
                ),
            ])
            ->columns(static::getColumnBreakpoints())
        ;
    }

    protected static function getOwnershipTab(): Tabs\Tab
    {
        return Tabs\Tab::make('Ownership')
            ->schema([
                static::getFormFieldTeam(),
                static::getFormFieldOwner(),
            ])
            ->columns(static::getColumnBreakpoints())
        ;
    }

}
