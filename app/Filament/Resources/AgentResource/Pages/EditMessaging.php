<?php

namespace App\Filament\Resources\AgentResource\Pages;

use App\Filament\Resources\AgentResource;
use App\Filament\Traits\HasColumns;
use App\Filament\Traits\HasFilters;
use Filament\Forms;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Support\Enums\IconPosition;

class EditMessaging extends EditTemplate
{
    use HasColumns;
    use HasFilters;

    protected static ?string $navigationLabel = 'Messaging';

    public function form(Form $form): Form
    {
        $canWhiteLabel = get_current_team()->getPlanOption('agent.white_label');
        return $form
            ->schema([
                Tabs::make('Tabs')
                    ->tabs([
                        static::getIntroTab(),
                        static::getFooterTab($canWhiteLabel),
                        static::getAttributionTab($canWhiteLabel),
                        static::getMetaTab($canWhiteLabel),
                    ])
                    ->persistTabInQueryString()
                    ->id('messaging')
            ])
            ->disabled(AgentResource::getRestrictEditingClosure())
            ->columns(1)
        ;
    }

    protected static function getIntroTab(): Tabs\Tab
    {
        $canSetQuestions = get_current_team()->getPlanOption('agent.custom_questions');
        return Tabs\Tab::make('Intro')
            ->icon('heroicon-s-language')
            ->iconPosition(IconPosition::Before)
            ->schema([
                AgentResource::getTranslateActions(
                    'intro',
                    [
                        'intro_preamble',
                        'intro_headline',
                        'intro_description',
                        'questions_title',
                    ],
                    true
                )
                    ->columnSpan(2)
                ,
                Grid::make(static::getColumnBreakpoints())
                    ->label('Intro')
                    ->relationship('frontend')
                    ->schema([
                        Forms\Components\Group::make()
                            ->schema([
                                TextInput::make('intro_preamble')
                                    ->label('Intro Preamble')
                                ,
                                TextInput::make('intro_headline')
                                    ->label('Intro Headline')
                                ,
                                TextInput::make('questions_title')
                                    ->label('Sample Questions Title')
                                    ->visible($canSetQuestions)
                                ,
                            ])
                        ,
                        Forms\Components\Group::make()
                            ->schema([
                                Textarea::make('intro_description')
                                    ->label('Intro Description')
                                    ->rows(5)
                                ,
                            ])
                        ,
                    ])
                    ->columnSpan(2)
                ,
            ])
            ->columns(static::getColumnBreakpoints())
        ;
    }

    protected static function getFooterTab(bool $canWhiteLabel): Tabs\Tab
    {
        $upgradeNotice = AgentResource::getUpgradeNotice();
        return Tabs\Tab::make('Footer')
            ->icon('heroicon-s-language')
            ->iconPosition(IconPosition::Before)
            ->schema([
                AgentResource::getTranslateActions(
                    'footer',
                    [
                        'footer_text',
                        'footer_cta_label',
                        'footer_cta_url',
                    ],
                    true
                )
                    ->visible($canWhiteLabel)
                    ->columnSpan(2)
                ,
                $upgradeNotice->visible(!$canWhiteLabel),
                Grid::make(static::getColumnBreakpoints())
                    ->relationship('frontend')
                    ->schema([
                        Forms\Components\TextInput::make('footer_text')
                            ->label('Footer Text')
                            ->maxLength(100)
                            ->columnSpan(2)
                        ,
                        static::getFormFieldActive(
                            false,
                            'hide_footer_cta',
                            'Hide CTA Button in Footer'
                        )
                            ->live()
                            ->columnSpan(2)
                        ,
                        static::getFormFieldUrl('footer_cta_url', 'Footer CTA URL')
                            ->disabled(fn (Get $get) => $get('hide_footer_cta'))
                            ->dehydrated(fn (Get $get) => !$get('hide_footer_cta'))
                            ->columnSpan(['default' => 2, 'md' => 1])
                        ,
                        Forms\Components\TextInput::make('footer_cta_label')
                            ->label('Footer CTA Label')
                            ->maxLength(50)
                            ->disabled(fn (Get $get) => $get('hide_footer_cta'))
                            ->dehydrated(fn (Get $get) => !$get('hide_footer_cta'))
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: "Defaults to 'Give'"
                            )
                            ->columnSpan(['default' => 2, 'md' => 1])
                        ,
                    ])
                    ->disabled(!$canWhiteLabel)
                    ->columnSpan(2)
                ,
            ])
            ->columns(static::getColumnBreakpoints())
        ;
    }

    protected static function getAttributionTab(bool $canWhiteLabel): Tabs\Tab
    {
        $upgradeNotice = AgentResource::getUpgradeNotice();
        return Tabs\Tab::make('Attribution')
            ->icon('heroicon-s-language')
            ->iconPosition(IconPosition::Before)
            ->schema([
                Grid::make(static::getColumnBreakpoints())
                    ->relationship('frontend')
                    ->schema([
                        AgentResource::getTranslateActions(
                            'attribution',
                            [
                                'creator_name',
                                'creator_url',
                                'creator_description',
                            ],
                            true
                        )
                            ->visible($canWhiteLabel)
                            ->columnSpan(2)
                        ,
                        $upgradeNotice->visible(!$canWhiteLabel),
                        TextInput::make('creator_name')
                            ->label('Creator Name')
                            ->maxLength(100)
                            ->columnSpan(['default' => 2, 'md' => 1])
                        ,
                        static::getFormFieldUrl('creator_url', 'Creator URL')
                            ->columnSpan(['default' => 2, 'md' => 1])
                        ,
                        Textarea::make('creator_description')
                            ->label('Creator Description')
                            ->rows(5)
                            ->maxLength(1000)
                            ->columnSpan(2)
                        ,
                    ])
                    ->disabled(!$canWhiteLabel)
                    ->columnSpan(2)
                ,
            ])
            ->columns(static::getColumnBreakpoints())
        ;
    }

    protected static function getMetaTab(bool $canWhiteLabel): Tabs\Tab
    {
        $upgradeNotice = AgentResource::getUpgradeNotice();
        return Tabs\Tab::make('Meta')
            ->icon('heroicon-s-language')
            ->iconPosition(IconPosition::Before)
            ->schema([
                Grid::make(static::getColumnBreakpoints())
                    ->relationship('frontend')
                    ->schema([
                        AgentResource::getTranslateActions(
                            'meta',
                            [
                                'meta_title',
                                'meta_description',
                                'meta_keywords',
                            ],
                            true
                        )
                            ->visible($canWhiteLabel)
                            ->columnSpan(2)
                        ,
                        $upgradeNotice->visible(!$canWhiteLabel),
                        TextInput::make('meta_title')
                            ->label('Meta Title')
                            ->maxLength(100)
                            ->columnSpan(['default' => 2, 'md' => 1])
                        ,
                        TextInput::make('meta_keywords')
                            ->label('Meta Keywords')
                            ->maxLength(500)
                            ->columnSpan(['default' => 2, 'md' => 1])
                        ,
                        Textarea::make('meta_description')
                            ->label('Meta Description')
                            ->rows(5)
                            ->maxLength(500)
                            ->columnSpan(2)
                        ,
                    ])
                    ->disabled(!$canWhiteLabel)
                    ->columnSpan(2)
                ,
            ])
            ->columns(static::getColumnBreakpoints())
        ;
    }

}
