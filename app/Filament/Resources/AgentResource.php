<?php

namespace App\Filament\Resources;

use App\Filament\RelationManagers\HistoryRelationManager;
use App\Filament\Resources\AgentResource\Pages\CreateRecord;
use App\Filament\Resources\AgentResource\Pages\EditAdmin;
use App\Filament\Resources\AgentResource\Pages\EditApi;
use App\Filament\Resources\AgentResource\Pages\EditBranding;
use App\Filament\Resources\AgentResource\Pages\EditEmbed;
use App\Filament\Resources\AgentResource\Pages\EditUi;
use App\Filament\Resources\AgentResource\Pages\EditMessaging;
use App\Filament\Resources\AgentResource\Pages\EditRecord;
use App\Filament\Resources\AgentResource\Pages\EditResponses;
use App\Filament\Resources\AgentResource\Pages\ListRecords;
use App\Filament\Resources\AgentResource\Pages\ShowHistory;
use App\Filament\Resources\AgentResource\RelationManagers\AgentCtasRelationManager;
use App\Filament\Resources\AgentResource\RelationManagers\AgentIntegrationsRelationManager;
use App\Filament\Resources\AgentResource\RelationManagers\AgentQuestionsRelationManager;
use App\Filament\Resources\AgentResource\RelationManagers\AgentTokensRelationManager;
use App\Filament\Resources\Resource as BaseResource;
use App\Filament\Traits\CanBeAdministered;
use App\Filament\Traits\CanBeLocked;
use App\Filament\Traits\CanBeModerated;
use App\Filament\Traits\CanBeOrdered;
use App\Filament\Traits\CanBePromoted;
use App\Filament\Traits\CanBeReplicated;
use App\Models\Agent;
use App\Models\Model;
use App\Services\Gatekeeper;
use Awcodes\Shout\Components\Shout;
use BezhanSalleh\FilamentShield\Contracts\HasShieldPermissions;
use dacoto\DomainValidator\Validator\Domain;
use Exception;
use Closure;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Pages\Page;
use Filament\Resources\Pages\PageRegistration;
use Filament\Support\Enums\Alignment;
use Filament\Support\Enums\IconPosition;
use Filament\Tables\Actions\Action as TableAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;

class AgentResource extends BaseResource implements HasShieldPermissions
{
    use CanBeAdministered;
    use CanBeLocked;
    use CanBeModerated;
    use CanBeOrdered;
    use CanBePromoted;
    use CanBeReplicated;
    use Translatable;

    protected static ?string $model = Agent::class;

    protected static ?string $recordTitleAttribute = 'name';

    protected static ?string $navigationIcon = 'heroicon-s-sparkles';

    protected static ?string $pluralModelLabel = 'Agents';

    protected static string $createLabel = 'create';

    protected static array $customPermissions = [
        'test',
    ];

    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    public static function canCreate(): bool
    {
        $team = get_current_team();
        return ($team->agents()->count() < $team->getPlanOption('agent.included_agents'));
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Tabs::make('Tabs')
                    ->tabs([
                        static::getSummaryTab(),
                        static::getLanguagesTab(),
                        static::getTranslationsTab(),
//                        static::getListingTab(),
                        static::getLicenseTab(),
                    ])
                    ->persistTabInQueryString()
                    ->id('agent'),
            ])
            ->disabled(static::getRestrictEditingClosure())
            ->columns(1)
        ;
    }

    /**
     * @throws Exception
     */
    public static function table(Table $table): Table
    {
        return static::decorateOrderableTable($table
            ->modifyQueryUsing(function (Builder $query) {
                static::$isScopedToTenant = false;
                if (! Gatekeeper::userCanAdminister(static::getModel())) {
                    $query->whereBelongsTo(get_current_team());
                }
            })
            ->columns([
                static::getTableColumnName(),
                TextColumn::make('target_worldview')
                    ->label('Target Worldview'),
                static::getTableColumnActive(),
                static::getTableColumnCreated(),
                static::getTableColumnUpdated(),
            ])
            ->filters([
                static::getTableFilterBoolean('is_active', 'Active'),
                static::getTableFilterBoolean('is_locked', 'Locked', 'Unlocked'),
                static::getTableFilterRelation('classification', 'Denominational Alignment'),
                static::getTableFilterArchived(),
                static::getTableFilterTeam(),
            ])
            ->actions([
                static::decorateLockAction(TableAction::make('Lock')),
                static::decorateUnlockAction(TableAction::make('Unlock')),
                static::decorateViewAction(
                    TableAction::make('View'),
                    fn ($record) => $record->is_active ? 'View Agent' : 'Agent Not Active',
                    fn ($record) => $record->is_active,
                    fn ($record) => $record->getUrl()
                ),
                static::getTableActionEditRow(),
            ])
            ->bulkActions(static::getTableBulkActions())
            ->paginated(static::$pageOptions)
            ->defaultPaginationPageOption(static::$defaultPageOption)
        );
    }

    protected static function getSummaryTab(): Tabs\Tab
    {

        $domainOptions = [];
        $domains = env_array('AGENT_ROOT_DOMAINS');
        if (Gatekeeper::isAdmin()) {
            $domains = array_merge(env_array('AGENT_ADMIN_ROOT_DOMAINS'), $domains);
        }
        foreach ($domains as $rootDomain) {
            $domainOptions[$rootDomain] = ".{$rootDomain}";
        }

        return Tabs\Tab::make('Summary')
            ->schema([

                Shout::make('agent_disabled_notice')
                    ->visible(fn ($record) => !is_null($record) && !$record->team->credits()->canSpend($record->getCreditUsage()))
                    ->content(fn ($record) => new HtmlString(view(
                        'components.notices.agent-deactivated',
                        [
                            'buy_credits_url' => route('filament.app.pages.billing.products', ['tenant'=>get_current_team_id()]),
                            'edit_team_url' => route('filament.app.resources.teams.edit', ['tenant'=>get_current_team_id(), 'record'=>get_current_team_id()]),
                        ]
                    )))
                    ->color('warning')
                    ->icon(false)
                    ->columnSpan(2)
                ,

                static::getFormFieldName(true, false)
                    ->live(onBlur: true)
                    ->afterStateUpdated(function (Get $get, Set $set, $state) {
                        if (empty($get('slug'))) {
                            $set('slug', Str::slug($state));
                        }
                    })
                ,

                static::getFormFieldActive('
                    Only active Agents will be visible to the public.
                    This Agent will be disabled if you enable the "pay-as-you-go" team option and run out of credits.
                ')
                    ->disabled(fn ($record) => !is_null($record) && !$record->team->credits()->canSpend($record->getCreditUsage()))
                ,

                Fieldset::make('domain')
                    ->id('domain')
                    ->schema([
                        static::getFormFieldSlug()
                            ->label(false)
                            ->required(false)
                            ->requiredWithout('vanity_domain')
                            ->label('Subdomain')
                            ->columnSpan(['default' => 3]),
                        Select::make('root_domain')
                            ->label('Root Domain')
                            ->options($domainOptions)
                            ->selectablePlaceholder(false)
                            ->default(array_keys($domainOptions)[0])
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'Agent will be available at [subdomain].[root_domain]. Note that each of the 2 parts are independently editable.',
                            )
                            ->columnSpan(['default' => 4]),
                    ])
                    ->label('Standard Subdomain')
                    ->columns(['default' => 7, 'xs' => 7, 'sm' => 7, 'md' => 7, 'lg' => 7, 'xl' => 7, '2xl' => 7])
                    ->columnSpan(1)
                ,

                TextInput::make('vanity_domain')
                    ->label('Vanity Domain')
                    ->maxLength(250)
                    ->rules([new Domain])
                    ->requiredWithout('slug')
                    ->unique(ignoreRecord: true, modifyRuleUsing: static::getUniqueByTeamAndTypeValidationClosure())
                    ->hintIcon(
                        'heroicon-s-question-mark-circle',
                        tooltip: 'Agent will also be available at the exact domain that you enter, as long as you also activate it using the directions below.'
                    )
                    ->helperText(view('components.help.agent-vanity-domain'))
                ,

            ])
            ->columns(static::getColumnBreakpoints())
        ;
    }

    protected static function getLanguagesTab(): Tabs\Tab
    {
        $rttCredits = env_int('REALTIME_TRANSLATION_CREDITS');
        return Tabs\Tab::make('Languages')
            ->schema([
                Grid::make(static::getColumnBreakpoints())
                    ->schema([
                        Select::make('supported_languages')
                            ->label('Supported Languages (leave blank for all)')
                            ->options(fn (Get $get, ?\Illuminate\Database\Eloquent\Model $record) => Agent::getAvailableLanguages(
                                $get('auto_translate'),
                                $record->model_id ?? null,
                                $record
                            ))
                            ->multiple()
                            ->live()
                        ,
                        Select::make('default_language')
                            ->label('Default Language')
                            ->options(fn (Get $get, ?Model $record) => Agent::getSupportedLanguages(
                                $get('supported_languages'),
                                $get('auto_translate'),
                                $record->model_id ?? null,
                                $record
                            ))
                            ->default(env('AGENT_DEFAULT_LANGUAGE'))
                            ->selectablePlaceholder(false)
                        ,
                        static::getUpgradeNotice()
                            ->visible(fn (Get $get) => !get_current_team()->getPlanOption('agent.realtime_translation'))
                        ,
                        static::getFormFieldActive(
                            new HtmlString("
                                <strong>Please note:</strong> Enabling this option will use an additional <strong>{$rttCredits} credits</strong>
                                per response for selected languages, or for <strong>all non-English languages</strong> if none are selected.
                            "),
                            'auto_translate',
                            'Use Real Time Translation for Specified Languages'
                        )
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'Expand language support to 192 languages by using a translation service to translate user prompts to English, and Agent responses back into the original prompt language.',
                            )
                            ->live()
                            ->disabled(!get_current_team()->getPlanOption('agent.realtime_translation'))
                        ,
                        Select::make('auto_translate_languages')
                            ->label('Real Time Translation Languages (leave blank for all)')
                            ->options(fn (Get $get, ?Model $record) => Agent::getRealTimeTranslationLanguages(
                                $get('supported_languages'),
                                [env('TRANSLATION_DEFAULT_LANGUAGE')]
                            ))
                            ->multiple()
                            ->disabled(fn (Get $get) => !$get('auto_translate'))
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'Only the languages selected here will use real time translation. English will never use it.',
                            )
                            ->live()
                        ,
                    ])
                ,
            ])
            ->columns(static::getColumnBreakpoints())
        ;
    }

    protected static function getTranslationsTab(): Tabs\Tab
    {
        $upgradeNotice = static::getUpgradeNotice();
        return Tabs\Tab::make('Bible Translations')
            ->schema([
                Grid::make(static::getColumnBreakpoints())
                    ->schema([
                        Select::make('supported_translations')
                            ->label('Supported Bible Translations (leave blank for all)')
                            ->options(config('agent.translations'))
                            ->multiple()
                            ->native(false)
                            ->live()
                        ,
                        Select::make('default_translation')
                            ->label('Default Bible Translation')
                            ->options(fn (Get $get, ?Model $record) => Agent::getSupportedTranslations($get('supported_translations'), $record))
                            ->default(env('AGENT_DEFAULT_TRANSLATION'))
                            ->selectablePlaceholder(false)
                            ->native(false)
                        ,
                    ])
                ,
            ])
            ->columns(static::getColumnBreakpoints())
        ;
    }

    protected static function getListingTab(): Tabs\Tab
    {
        $upgradeNotice = static::getUpgradeNotice();
        return Tabs\Tab::make('Listing')
            ->icon('heroicon-s-language')
            ->iconPosition(IconPosition::Before)
            ->schema([
                AgentResource::getTranslateActions(
                    'listing',
                    [
                        'persona_name',
                        'persona_tagline',
                        'persona_description',
                        'target_worldview'
                    ]
                )
                    ->columnSpan(2)
                ,
                Group::make()
                    ->schema([
                        static::getFormFieldActive(
                            'Advertise this Agent publicly on Apologist Agent Marketplace. You must submit your Agent for approval in order for it to be listed on the Marketplace.',
                            'marketplace_active',
                            new HtmlString("List on <a href='' target='_blank' class='hover:underline text-gray-900 dark:text-white font-bold'>Apologist Agent Marketplace ↗</a>.")
                        ),
                        TextInput::make('persona_name')
                            ->label('Name')
                        ,
                    ])
                ,
                Group::make()
                    ->schema([
                        static::getFormFieldAvatar('agents/personas', 'Avatar', 'persona_avatar_path'),
                    ])
                    ->columnSpan(['default' => 2, 'md' => 1]),
                TextInput::make('persona_tagline')
                    ->label('Tagline')
                ,
                TextInput::make('target_worldview')
                    ->label('Target Worldview')
                    ->maxLength(100)
                ,
                static::getFormFieldBlockRichText('persona_description')
                    ->label('Description')
                ,
            ])
            ->columns(static::getColumnBreakpoints())
        ;
    }

    protected static function getLicenseTab(): Tabs\Tab
    {
        $canSetInstructions = get_current_team()->getPlanOption('agent.custom_instructions');
        $upgradeNotice = AgentResource::getUpgradeNotice();
        return Tabs\Tab::make('License')
            ->schema([
                $upgradeNotice->visible(!$canSetInstructions),
                static::getFormFieldActive(
                    "Allow users to view this Agent's Instructions and Sources for transparency. ",
                    'is_open_source',
                    'This Agent is Open Source'
                )
                    ->live()
                ,
                static::getFormFieldUrl('license_url', 'License URL')
                    ->hintIcon(
                        'heroicon-s-question-mark-circle',
                        tooltip: 'A URL to learn more about the open source license.',
                    )
                    ->disabled(fn (Get $get) => !$get('is_open_source'))
                ,
            ])
            ->disabled(!$canSetInstructions)
            ->columns(static::getColumnBreakpoints())
        ;
    }

    public static function getRestrictEditingClosure(): Closure
    {
        return (fn ($record) => restrict_editing($record));
    }

    public static function getUpgradeNotice(): Shout
    {
        return Shout::make('agent_upgrade_notice')
            ->content(fn ($record) => new HtmlString(view(
                'components.notices.agent-upgrade',
                [
                    'change_subscription_url' => route('filament.app.pages.billing.subscription', ['tenant'=>get_current_team_id()]),
                ]
            )))
            ->color('warning')
            ->icon(false)
            ->columnSpan(2)
        ;
    }

    public static function getTranslateActions(string $tab, array $fields, bool $isFrontend = false): Actions
    {
        $defaultLanguage = config('agent.languages')[env('AGENT_DEFAULT_LANGUAGE')];

        return Actions::make([

            Action::make("{$tab}_translate")
                ->extraAttributes(['id' => btn_id("translate-{$tab}")])
                ->label('Translate All')
                ->icon('heroicon-s-language')
                ->outlined()
                ->action(function (Agent $record, $livewire, Get $get, Set $set) use ($fields, $isFrontend) {
                    $fieldPrefix = '';
                    $model = $record;
                    if ($isFrontend) {
                        $fieldPrefix = 'frontend.';
                        $model = $record->frontend;
                    }
                    $lang = $livewire->getActiveActionsLocale();
                    if ($model) {
                        foreach ($fields as $field) {
                            $formField = $fieldPrefix . $field;
                            $set(
                                $formField,
                                $livewire->translateFormField(
                                    $lang,
                                    $get($formField),
                                    $model->getTranslation($field, $lang)
                                )
                            );
                        }
                    }
                })
            ,

            Action::make("{$tab}_reset_translation")
                ->extraAttributes(['id' => btn_id("reset-translation-{$tab}")])
                ->label("Reset All to {$defaultLanguage}")
                ->icon('heroicon-s-arrow-path')
                ->outlined()
                ->action(function (Agent $record, $livewire, Get $get, Set $set) use ($fields, $isFrontend) {
                    $fieldPrefix = '';
                    $model = $record;
                    if ($isFrontend) {
                        $fieldPrefix = 'frontend.';
                        $model = $record->frontend;
                    }
                    if ($model) {
                        foreach ($fields as $field) {
                            $formField = $fieldPrefix . $field;
                            $set(
                                $formField,
                                $model->getTranslation($field, env('AGENT_DEFAULT_LANGUAGE'))
                            );
                        }
                    }
                })
            ,

        ])
            ->alignment(Alignment::Center)
        ;
    }

    /**
     * @return string[]
     */
    public static function getRelations(): array
    {
        return [
            AgentQuestionsRelationManager::class,
            AgentCtasRelationManager::class,
            AgentIntegrationsRelationManager::class,
            AgentTokensRelationManager::class,
            HistoryRelationManager::class,
        ];
    }

    /**
     * @return array|PageRegistration[]
     *
     * @throws Exception
     */
    public static function getPages(): array
    {
        return [
            'index' => ListRecords::route('/', ['tenant' => get_current_team_id()]),
            'create' => CreateRecord::route('/create', ['tenant' => get_current_team_id()]),
            'edit' => EditRecord::route('/{record}/edit', ['tenant' => get_current_team_id()]),
            'responses' => EditResponses::route('/{record}/responses', ['tenant' => get_current_team_id()]),
            'ui' => EditUi::route('/{record}/ui', ['tenant' => get_current_team_id()]),
            'messaging' => EditMessaging::route('/{record}/messaging', ['tenant' => get_current_team_id()]),
            'branding' => EditBranding::route('/{record}/branding', ['tenant' => get_current_team_id()]),
            'embed' => EditEmbed::route('/{record}/embed', ['tenant' => get_current_team_id()]),
            'api' => EditApi::route('/{record}/api', ['tenant' => get_current_team_id()]),
            'admin' => EditAdmin::route('/{record}/admin', ['tenant' => get_current_team_id()]),
            'history' => ShowHistory::route('/{record}/history', ['tenant' => get_current_team_id()]),
        ];
    }

    public static function getRecordSubNavigation(Page $page): array
    {
        $pages = [
            EditRecord::class,
            EditResponses::class,
            EditMessaging::class,
            EditBranding::class,
            EditUi::class,
            EditEmbed::class,
            EditApi::class,
        ];
        if (Gatekeeper::isAdmin()) {
            $pages[] = EditAdmin::class;
        }
        return $page->generateNavigationItems($pages);
    }

}
