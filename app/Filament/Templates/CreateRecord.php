<?php

namespace App\Filament\Templates;

use App\Filament\Traits\HasProductTour;
use Filament\Actions\Action;
use Filament\Resources\Pages\CreateRecord as BaseCreateRecord;
use Illuminate\Support\Str;
use JibayMcs\FilamentTour\Tour\HasTour;

class CreateRecord extends BaseCreateRecord
{
    use HasProductTour;
    use HasTour;

    protected function getActionLabel(): string
    {
        return Str::apa(static::getResource()::getCreateLabel() . ' ' . static::getResource()::getModelLabel());
    }

    public function getBreadcrumb(): string
    {
        return Str::apa(static::getResource()::getCreateLabel());
    }

    public function getTitle(): string
    {
        return $this->getActionLabel();
    }

    protected function getCreateFormAction(): Action
    {
        return parent::getCreateFormAction()
            ->label($this->getActionLabel());
    }

    protected function getCreateAnotherFormAction(): Action
    {
        return parent::getCreateAnotherFormAction()
            ->label(Str::apa(implode(
                ' ',
                [
                    static::getResource()::getCreateLabel(),
                    'and',
                    static::getResource()::getCreateLabel(),
                    'another',
                ]
            )));
    }

    protected function getCreatedNotificationTitle(): ?string
    {
        return Str::apa(static::getResource()::getModelLabel().' '.static::getResource()::getCreateLabelPast());
    }
}
