<?php
namespace App\Models;

use Altek\Accountant\Contracts\Recordable;
use Altek\Accountant\Recordable as RecordsToLedger;
use Altek\Eventually\Eventually as RecordsPivotsToLedger;
use App\Models\Traits\HasImage;
use App\Models\Traits\SyncsToFrontend;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Spatie\Translatable\HasTranslations;

class AgentFrontend extends Model implements Recordable
{

    use HasFactory;
    use HasTranslations;
    use HasImage;
    use RecordsPivotsToLedger;
    use RecordsToLedger;
    use SoftDeletes;
    use SyncsToFrontend;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [

        'agent_id',

        // Branding
        'theme',
        'primary_color',
        'image_path',
        'background_color',
        'background_path',
        'display_font_url',
        'display_font_name',
        'body_font_url',
        'body_font_name',

        // Meta
        'favicon_path',
        'meta_title',
        'meta_description',
        'meta_keywords',

        // Attribution
        'creator_name',
        'creator_description',
        'creator_url',

        // App
        'app_icon_color',
        'app_icon_path',

        // Footer
        'footer_text',
        'hide_footer_cta',
        'footer_cta_label',
        'footer_cta_url',

        // Embed
        'embed_hide_header',
        'embed_icon_color',
        'embed_icon_path',

        // Media
        'show_media',

        // Intro
        'intro_preamble',
        'intro_headline',
        'intro_description',
        'questions_title',

        // Custom Code
        'custom_styles',
        'custom_scripts',

        // Security
        'has_basic_auth',
        'basic_auth_user',
        'basic_auth_password',

    ];

    /**
     * @var string[]
     */
    public array $translatable = [

        // Attribution
        'creator_name',
        'creator_description',
        'creator_url',

        // Meta
        'meta_title',
        'meta_description',
        'meta_keywords',

        // Intro
        'intro_preamble',
        'intro_headline',
        'intro_description',
        'questions_title',

        // Footer
        'footer_text',
        'footer_cta_label',
        'footer_cta_url',

    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [

            // Creator
            'creator_name' => 'array',
            'creator_description' => 'array',
            'creator_url' => 'array',

            // Meta
            'meta_title' => 'array',
            'meta_description' => 'array',
            'meta_keywords' => 'array',

            // Embed
            'embed_hide_header' => 'boolean',

            // Intro
            'intro_preamble' => 'array',
            'intro_headline' => 'array',
            'intro_description' => 'array',
            'questions_title' => 'array',

            // Footer
            'footer_text' => 'array',
            'hide_footer_cta' => 'boolean',
            'footer_cta_label' => 'array',
            'footer_cta_url' => 'array',

            // Media
            'show_media' => 'boolean',

            // Security
            'has_basic_auth' => 'boolean',
            'basic_auth_password' => 'encrypted',

        ];
    }

    public static function booted(): void
    {
        parent::booted();
        static::creating(function (Model $model) {
            if (is_null($model->primary_color)) {
                $model->primary_color = env('AGENT_DEFAULT_PRIMARY_COLOR');
            }
        });
    }

    public function agent(): BelongsTo
    {
        return $this->belongsTo(Agent::class);
    }

    public function mediaCollections(): BelongsToMany
    {
        return $this->belongsToMany(Collection::class, 'agent_frontend_media_collection', 'agent_frontend_id', 'media_collection_id');
    }

    public function syncToFrontend(): void
    {

        increase_timeout('MAX');

        $this->pushFieldsToFrontend();
        $this->pruneDeletedFromFrontend();

        DB::connection(env('FRONTEND_DB_CONNECTION'))
            ->table($this->getTable())
            ->where('id', $this->id)
            ->update([
                'media_collections' => $this->mediaCollections()->pluck('collections.id'),
            ])
        ;

    }

    /* TODO: these are accessors to compensate for Filament's poor handling of relation file uploads */
    public function getImagePathAttribute($value): array|string|null { return $this->getFilePathAttribute($value); }
    public function getFaviconPathAttribute($value): array|string|null { return $this->getFilePathAttribute($value); }
    public function getBackgroundPathAttribute($value): array|string|null { return $this->getFilePathAttribute($value); }
    public function getAppIconPathAttribute($value): array|string|null { return $this->getFilePathAttribute($value); }
    public function getEmbedIconPathAttribute($value): array|string|null { return $this->getFilePathAttribute($value); }
    protected function getFilePathAttribute($value): array|string|null
    {
        if ($value && is_string($value)) {
            return [$value];
        }
        return $value;
    }

    /* TODO: these are mutators to compensate for Filament's poor handling of relation file uploads */
    public function setImagePathAttribute($value): void { $this->setFilePathAttribute('image_path', $value); }
    public function setFaviconPathAttribute($value): void { $this->setFilePathAttribute('favicon_path', $value); }
    public function setBackgroundPathAttribute($value): void { $this->setFilePathAttribute('background_path', $value); }
    public function setAppIconPathAttribute($value): void { $this->setFilePathAttribute('app_icon_path', $value); }
    public function setEmbedIconPathAttribute($value): void { $this->setFilePathAttribute('embed_icon_path', $value); }
    protected function setFilePathAttribute($key, $value): void
    {
        $this->attributes[$key] = is_array($value) ? ($value[0] ?? null) : $value;
    }

}
