<?php

namespace App\Models;

use App\Jobs\InvalidateFrontendCacheJob;
use App\Jobs\ManageListingSearchDocumentJob;
use App\Jobs\SyncFrontendListingUrlJob;
use App\Jobs\SyncToFrontendJob;
use App\Models\Traits\BelongsToTeam;
use App\Models\Traits\BelongsToUser;
use App\Models\Traits\HasFrontendListing;
use App\Models\Traits\HasSlug;
use App\Models\Traits\SyncsToFrontend;
use CyrildeWit\EloquentViewable\InteractsWithViews;
use Illuminate\Database\Eloquent\Model as BaseModel;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class Model extends BaseModel
{
    public static string $defaultSort = 'name';

    protected array $ignored = [];

    public static function booted(): void
    {

        static::creating(function (Model $model) {

            $model->setDefaultAttributes();

            if (
                uses_trait($model, HasSlug::class) &&
                empty($model->slug)
            ) {
                $model->slug = $model->getSlug();
            }

        });

        static::saved(fn (Model $model) => static::touched($model));
        static::deleted(fn (Model $model) => static::touched($model));
        if (uses_trait(static::class, SoftDeletes::class)) {
            static::forceDeleting(fn (Model $model) => static::touched($model));
//            static::restored(fn (Model $model) => static::touched($model));
        }

    }

    protected static function touched(Model $model): void
    {
        if (!empty($model->getDirty())) {

//            Log::debug($model->getDirty());
            if (uses_trait($model, HasFrontendListing::class)) {

                dispatch(new ManageListingSearchDocumentJob($model));

                if ($model->is_approved && $model->social_active) {
                    dispatch(new SyncFrontendListingUrlJob($model));
                    dispatch(new InvalidateFrontendCacheJob($model));
                }

            }

            if (uses_trait($model, SyncsToFrontend::class)) {
                dispatch_sync(new SyncToFrontendJob($model));
            }

        }
    }

    public function setDefaultAttributes(): void
    {
        if (auth()->check()) {
            if (uses_trait($this, BelongsToTeam::class)) {
                $this->setDefaultTeam();
            }
            if (uses_trait($this, BelongsToUser::class)) {
                $this->setDefaultOwner();
            }
        }
    }

    public function hasChanged(string|array $attrs): bool
    {
        if ($this->wasRecentlyCreated) {
            return true;
        }
        if (! is_array($attrs)) {
            $attrs = [$attrs];
        }
        $changed = array_keys($this->getChanges());

        return count(array_intersect($changed, $attrs)) > 0;
    }

    public static function recordsViews(): bool
    {
        return uses_trait(static::class, InteractsWithViews::class);
    }

    public function getIgnored(): array
    {
        return $this->ignored;
    }

    protected function getIndirectRelations(Relation $qry, $relationName = null): array
    {
        $relations = [];
        $models = $qry->get();
        foreach ($models as $model) {
            if (! is_null($relationName)) {
                $relations[$model->$relationName->id] = $model->$relationName->name;
            } else {
                $relations[$model->id] = $model->name;
            }
        }

        return $relations;
    }

    public function updateTimestamp(string $field): int
    {
        $attrs = [];
        $attrs[$field] = DB::raw('NOW()');

        return DB::table($this->getTable())->where('id', $this->id)->update($attrs);
    }

}
