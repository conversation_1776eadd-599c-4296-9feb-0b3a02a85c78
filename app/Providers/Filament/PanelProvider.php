<?php

namespace App\Providers\Filament;

use App\Http\Middleware\Authenticate;
use App\Http\Middleware\ExtendAccessToken;
use Filament\Enums\ThemeMode;
use Filament\FontProviders\GoogleFontProvider;
use Filament\Forms\Components\Field;
use Filament\Forms\Components\FileUpload;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Navigation\MenuItem;
use Filament\Navigation\NavigationBuilder;
use Filament\Navigation\NavigationGroup;
use Filament\Navigation\NavigationItem;
use Filament\Panel;
use Filament\PanelProvider as BasePanelProvider;
use Filament\Support\Colors\Color;
use Filament\Support\Facades\FilamentAsset;
use Filament\View\PanelsRenderHook;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Str;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Jeffgreco13\FilamentBreezy\BreezyCore;
use pxlrbt\FilamentEnvironmentIndicator\EnvironmentIndicatorPlugin;

abstract class PanelProvider extends BasePanelProvider
{
    public function boot(): void
    {

        FileUpload::configureUsing(function (FileUpload $field): void {
            $field->openable()->deletable();
        });

        // Disable all form fields on trashed (archived) models
        Field::configureUsing(function (Field $field): void {
            $field->disabled(function (?Model $record = null) {
                return ! is_null($record) && method_exists($record, 'trashed') && $record->trashed();
            });
        });

        FilamentAsset::registerCssVariables([
            'background-image' => secure_asset('img/background-md-dark.png'),
        ]);

    }

    protected function apgUi(Panel $panel, ?array $nav = null): Panel
    {
        return $panel
            ->viteTheme('resources/css/filament/app/theme.css')
            ->unsavedChangesAlerts()
            ->authGuard('web')
            ->colors([
                'primary' => [
                    50 => '#f2f0ff',
                    100 => '#e8e4ff',
                    200 => '#d4ccff',
                    300 => '#b5a4ff',
                    400 => '#9270ff',
                    500 => '#7137ff',
                    600 => '#630fff',
                    700 => '#5500ff',
                    800 => '#4600da',
                    900 => '#330099',
                    950 => '#21007a',
                ],
            ])
            ->font('Inter', provider: GoogleFontProvider::class)
            ->defaultThemeMode(ThemeMode::Dark)
            ->brandLogoHeight('3rem')
            ->favicon(secure_asset('img/favicon.svg', true))
            ->spa()
            ->navigation(function (NavigationBuilder $builder): NavigationBuilder|false {
                $nav = $this->getNav();
                if (is_array($nav)) {
                    $groups = $this->buildNavGroups($nav);

                    return $builder->groups($groups);
                }

                return false;
            })
            ->userMenuItems([
                'profile' => MenuItem::make()
                    ->url(fn (): string => route('filament.id.pages.profile'))
                    ->label('My Profile'),
            ])
            ->renderHook(PanelsRenderHook::BODY_START, fn () => view('components.app.impersonate-banner'))
            ->renderHook(PanelsRenderHook::USER_MENU_PROFILE_AFTER, fn () => Blade::render('@livewire(\'user-menu-items\')'));
    }

    protected function buildNavGroups(array $nav): array
    {
        $groups = [];
        foreach ($nav as $groupLabel => $groupAttrs) {

            if (isset($groupAttrs['items'])) {
                $groupItems = $groupAttrs['items'];
                $groupIcon = $groupAttrs['icon'] ?? null;
            } else {
                $groupItems = $groupAttrs;
                $groupIcon = false;
            }

            $addGroup = true;
            if (isset($groupAttrs['visibleClosure'])) {
                $addGroup = $groupAttrs['visibleClosure']();
            }

            if ($addGroup) {

                $items = [];
                foreach ($groupItems as $itemLabel => $itemConfig) {
                    if (is_numeric($itemLabel)) {
                        $items[] = call_user_func("{$itemConfig}::getNavigationItems")[0];
                    } else {

                        $params = $itemConfig['params'] ?? [];
                        $tmpItem = NavigationItem::make($itemLabel)
                            ->icon(isset($itemConfig['icon']) ? "heroicon-s-{$itemConfig['icon']}" : null)
                            ->url(fn (): string => $itemConfig['url'] ?? route($itemConfig['route'], array_merge(['tenant' => auth()->user()->currentTeam->id], $params)))
                            ->isActiveWhen(fn () => (
                                (isset($itemConfig['route']) && request()->routeIs($itemConfig['route'])) ||
                                (isset($itemConfig['url']) && ($itemConfig['url'] == request()->url()))
                            ))
                            ->openUrlInNewTab(isset($itemConfig['tab']) && $itemConfig['tab']);

                        if (isset($itemConfig['visibleClosure'])) {
                            $tmpItem = $tmpItem->visible($itemConfig['visibleClosure']);
                        }

                        $items[] = $tmpItem;

                    }
                }

                $isDefault = ($groupLabel == 'default');
                $groupClass = 'fi-sidebar-group-'.Str::kebab($groupLabel);
                $group = NavigationGroup::make($groupLabel)
                    ->items($items)
                    ->label($isDefault ? null : $groupLabel)
                    ->icon($groupIcon ? "heroicon-s-{$groupIcon}" : false)
                    ->collapsed(false)
                    ->collapsible(true)
                    ->extraSidebarAttributes([
                        'class' => $groupClass,
                    ])
                ;

                $groups[] = $group;

            }

        }

        return $groups;
    }

    protected function apgMiddleware(Panel $panel): Panel
    {
        return $panel
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
                ExtendAccessToken::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ]);
    }

    protected function getPlugins(): array
    {
        return [];
    }

    protected function getNav(): ?array
    {
        return null;
    }

    protected function getAuthPlugin(): BreezyCore
    {
        return BreezyCore::make()
            ->myProfile(
                hasAvatars: true,
                slug: 'profile',
            )
            ->avatarUploadComponent(function () {
                return FileUpload::make('avatar_path')
                    ->directory('users')
                    ->image()
                    ->avatar()
                    ->circleCropper()
                    ->label('Profile Photo');
            })
            ->enableTwoFactorAuthentication()
            ->enableSanctumTokens(
                false,
                [

                    // Categories
                    'category:pagination' => 'List Categories',
                    'category:detail' => 'View Category',

                    // Collections
                    'collection:pagination' => 'List Collections',
                    'collection:detail' => 'View Collection',
                    'collection:create' => 'Create Collection',
                    'collection:update' => 'Edit Collection',
                    'collection:delete' => 'Delete Collection',

                    // Contributors
                    'contributor:pagination' => 'List Contributors',
                    'contributor:detail' => 'View Contributor',
                    'contributor:create' => 'Add Contributor',
                    'contributor:update' => 'Edit Contributor',
                    'contributor:delete' => 'Remove Contributor',

                    // Sources
                    'source:pagination' => 'List Sources',
                    'source:detail' => 'View Source',
                    'source:create' => 'Add Source',
                    'source:update' => 'Edit Source',
                    'source:delete' => 'Remove Source',

                    // Source Contributions
                    'source-contribution:pagination' => 'List Contributions',
                    'source-contribution:detail' => 'View Contribution',
                    'source-contribution:create' => 'Add Contribution',
                    'source-contribution:update' => 'Edit Contribution',
                    'source-contribution:delete' => 'Remove Contribution',

                    // Organization
                    'organization:pagination' => 'List Organizations',
                    'organization:detail' => 'View Organization',
                    'organization:create' => 'Add Organization',
                    'organization:update' => 'Edit Organization',
                    'organization:delete' => 'Remove Organization',

                    // Discussion
                    'discussion:pagination' => 'List Discussions',
                    'discussion:detail' => 'View Discussion',
                    'discussion:create' => 'Add Discussion',
                    'discussion:update' => 'Edit Discussion',
                    'discussion:delete' => 'Remove Discussion',

                ]
            );
    }

    protected function getEnvironmentIndicatorPlugin(): EnvironmentIndicatorPlugin
    {
        return EnvironmentIndicatorPlugin::make()
            ->visible(fn () => true)
            ->color(fn () => match (app()->environment()) {
                'local' => Color::Green,
                'development' => Color::Orange,
                'production' => null,
                default => Color::Red,
            })
            ->showBorder(false);
    }

    /**
     * @return string[]
     */
    protected function getListingScopes(): array
    {
        return array_merge(
            $this->getAgentListingScopes(),
            $this->getOrganizationListingScopes(),
            $this->getContributorListingScopes(),
            $this->getCollectionListingScopes(),
            $this->getSourceListingScopes(),
        );
    }

    protected function getAgentCreateScopes(): array
    {
        return [
            \App\Filament\Resources\AgentResource\Pages\CreateRecord::class,
        ];
    }

    protected function getAgentEditScopes(): array
    {
        return [
            \App\Filament\Resources\AgentResource\Pages\EditAdmin::class,
            \App\Filament\Resources\AgentResource\Pages\EditApi::class,
            \App\Filament\Resources\AgentResource\Pages\EditBranding::class,
            \App\Filament\Resources\AgentResource\Pages\EditEmbed::class,
            \App\Filament\Resources\AgentResource\Pages\EditMessaging::class,
            \App\Filament\Resources\AgentResource\Pages\EditRecord::class,
            \App\Filament\Resources\AgentResource\Pages\EditResponses::class,
            \App\Filament\Resources\AgentResource\Pages\EditUi::class,
        ];
    }

    /**
     * @return string[]
     */
    protected function getAgentListingScopes(): array
    {
        return [
            \App\Filament\Resources\AgentResource\Pages\EditRecord::class,
            \App\Filament\Resources\AgentResource\Pages\EditUi::class,
        ];
    }

    /**
     * @return string[]
     */
    protected function getAgentUsageScopes(): array
    {
        return [
//            \App\Filament\Resources\AgentResource\Pages\ListRecords::class,
//            \App\Filament\Resources\AgentResource\Pages\EditRecord::class,
        ];
    }

    /**
     * @return string[]
     */
    protected function getOrganizationListingScopes(): array
    {
        return [
            \App\Filament\Resources\OrganizationResource\Pages\EditRecord::class,
        ];
    }

    /**
     * @return string[]
     */
    protected function getContributorListingScopes(): array
    {
        return [
            \App\Filament\Resources\ContributorResource\Pages\EditRecord::class,
        ];
    }

    /**
     * @return string[]
     */
    protected function getCollectionListingScopes(): array
    {
        return [
            \App\Filament\Resources\CollectionResource\Pages\EditRecord::class,
            \App\Filament\Resources\AnthologyCollectionResource\Pages\EditRecord::class,
            \App\Filament\Resources\ChannelCollectionResource\Pages\EditRecord::class,
            \App\Filament\Resources\ListCollectionResource\Pages\EditRecord::class,
            \App\Filament\Resources\PeriodicalCollectionResource\Pages\EditRecord::class,
            \App\Filament\Resources\PodcastCollectionResource\Pages\EditRecord::class,
            \App\Filament\Resources\RssCollectionResource\Pages\EditRecord::class,
            \App\Filament\Resources\SeriesCollectionResource\Pages\EditRecord::class,
            \App\Filament\Resources\WebsiteCollectionResource\Pages\EditRecord::class,
        ];
    }

    /**
     * @return string[]
     */
    protected function getSourceListingScopes(): array
    {
        return [
            \App\Filament\Resources\SourceResource\Pages\EditRecord::class,
            \App\Filament\Resources\ArticleSourceResource\Pages\EditRecord::class,
            \App\Filament\Resources\BookSourceResource\Pages\EditRecord::class,
            \App\Filament\Resources\EpisodeSourceResource\Pages\EditRecord::class,
            \App\Filament\Resources\MediaSourceResource\Pages\EditRecord::class,
            \App\Filament\Resources\UrlSourceResource\Pages\EditRecord::class,
            \App\Filament\Resources\YoutubeSourceResource\Pages\EditRecord::class,
        ];
    }

    /**
     * @return string[]
     */
    protected function getListingAnalyticsScopes(): array
    {
        return [
            \App\Filament\Pages\App\Analytics\AgentAnalytics::class,
        ];
    }

}
