<?php

use Illuminate\Support\Facades\Schedule;

$envs = ['production', 'staging'];

// Self-heal / fix stuff
Schedule::hourly()
    ->environments($envs)
    ->group(function() {

        // Import any sources whose initial import got interrupted
        Schedule::command('fix:sources placeholder')
            ->emailOutputOnFailure(env('ADMIN_EMAIL'))
        ;

        // Get any processing transcripts that went beyond 30m
        Schedule::command('import:transcripts')
            ->emailOutputOnFailure(env('ADMIN_EMAIL'));

        // Index search corpus documents
        Schedule::command('fix:sources search --throttle=3')
            ->emailOutputTo(env('ADMIN_EMAIL'))
        ;

        // Index RAG corpus documents
        Schedule::command('fix:sources rag --throttle=3')
            ->emailOutputOnFailure(env('ADMIN_EMAIL'))
        ;

    })
;

// Sync Agent prompts from frontend
Schedule::twiceDaily()
    ->environments($envs)
    ->group(function() {

        // Sync prompts
        Schedule::command('sync:agent-prompts')
            ->emailOutputOnFailure(env('ADMIN_EMAIL'))
        ;

        // Sync prompts
        Schedule::command('sync:agent-searches')
            ->emailOutputOnFailure(env('ADMIN_EMAIL'))
        ;

    })
;

Schedule::environments($envs)
    ->group(function() {

        // Backup files
        $ts = date('Y-m-d-H-i-s');
        Schedule::command("backup:run --only-files --filename={$ts}_files.zip")
            ->dailyAt('00:00')
            ->emailOutputTo(env('ADMIN_EMAIL'));

        // Clean backups
        Schedule::command('backup:clean')
            ->dailyAt('01:00')
            ->emailOutputTo(env('ADMIN_EMAIL'));

        // Sync Agents
//        Schedule::command('sync:agents')
//            ->emailOutputTo(env('ADMIN_EMAIL'));

        // Sync social views
        Schedule::command('sync:social-views')
            ->dailyAt('02:00');

        // Sync frontend impressions
        Schedule::command('sync:agent-impressions')
            ->dailyAt('03:00');

        // Sync frontend stats
        Schedule::command('sync:frontend-stats')
            ->dailyAt('04:00');

        // Import new articles from RSS feeds
        Schedule::command('import:rss_feeds')
            ->dailyAt('05:00');

        // Scrape websites for new content
        Schedule::command('import:websites')
            ->dailyAt('06:00');

        // Import new podcast episodes
        Schedule::command('import:podcast_episodes')
            ->dailyAt('07:00');

        // Import new YouTube videos
        Schedule::command('import:channels')
            ->dailyAt('08:00');

        // Run daily notifications
        Schedule::command('notify:changes daily')
            ->dailyAt('09:00');

        // Prune telescope
        Schedule::command('telescope:prune --hours=720')
            ->dailyAt('10:00');

        // Renew subscription entitlements
        Schedule::command('spike:renew-subscription-providables')
            ->dailyAt('11:00')
            ->emailOutputTo(env('ADMIN_EMAIL'));

        // Calculate Agent prompts
        Schedule::command('bill:agent-prompts --verbose')
            ->dailyAt('12:00')
            ->emailOutputTo(env('ADMIN_EMAIL'));

        // Calculate Agent searches
        Schedule::command('bill:agent-searches --verbose')
            ->dailyAt('13:00')
            ->emailOutputTo(env('ADMIN_EMAIL'));

        // Show team balances
        Schedule::command('bill:team-balances')
            ->dailyAt('14:00')
            ->emailOutputTo(env('ADMIN_EMAIL'));

        // Re-import any sources whose image wasn't saved properly
        Schedule::command('fix:sources image')
            ->dailyAt('15:00')
            ->emailOutputTo(env('ADMIN_EMAIL'));

        // Delete any URLs from the same team with the same URL and name
        Schedule::command('fix:sources duplicate')
            ->dailyAt('16:00')
            ->emailOutputOnFailure(env('ADMIN_EMAIL'));

        // Transcribe any media that was supposed to be but wasn't
        Schedule::command('fix:sources transcript')
            ->dailyAt('17:00')
            ->emailOutputOnFailure(env('ADMIN_EMAIL'));

        // Import any URLs that are supposed to have text but don't
        Schedule::command('fix:sources text')
            ->dailyAt('18:00')
            ->emailOutputOnFailure(env('ADMIN_EMAIL'));

        // Delete any URLs from the same team with the same URL and name
        Schedule::command('fix:sources terms')
            ->dailyAt('19:00')
            ->emailOutputOnFailure(env('ADMIN_EMAIL'));

    })
;

Schedule::command('notify:changes weekly')
    ->weekly()
    ->environments($envs)
    ->emailOutputTo(env('ADMIN_EMAIL'))
;
