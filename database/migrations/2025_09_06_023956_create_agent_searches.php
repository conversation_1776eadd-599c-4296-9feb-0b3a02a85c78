<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('agent_prompts', function (Blueprint $table) {
            $table->bigInteger('id')->unsigned()->primary()->change();
        });
        Schema::create('agent_searches', function (Blueprint $table) {
            $table->unsignedBigInteger('id')->primary();
            $table->string('query');
            $table->jsonb('filters')->nullable();
            $table->jsonb('results')->nullable();
            $table->unsignedBigInteger('agent_id');
            $table->foreign('agent_id')->references('id')->on('agents');
            $table->unsignedBigInteger('agent_token_id')->nullable();
            $table->foreign('agent_token_id')->references('id')->on('agent_tokens');
            $table->unsignedBigInteger('agent_prompt_id')->nullable();
            $table->foreign('agent_prompt_id')->references('id')->on('agent_prompts');
            $table->timestamp('searched_at');
            $table->timestamp('synced_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('agent_searches');
    }
};
