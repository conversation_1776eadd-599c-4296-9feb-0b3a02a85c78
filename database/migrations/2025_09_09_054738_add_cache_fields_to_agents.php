<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('agents', function (Blueprint $table) {
            $table->unsignedInteger('completion_cache_ttl')->nullable()->after('strip_markdown');
            $table->unsignedInteger('search_cache_ttl')->nullable()->after('completion_cache_ttl');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('agents', function (Blueprint $table) {
            $table->dropColumn('completion_cache_ttl');
            $table->dropColumn('search_cache_ttl');
        });
    }
};
