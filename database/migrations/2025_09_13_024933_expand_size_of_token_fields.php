<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('teams', function (Blueprint $table) {
            $table->string('corpus_api_key', 1000)->nullable()->change();
        });
        Schema::table('agent_tokens', function (Blueprint $table) {
            $table->string('token', 1000)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('teams', function (Blueprint $table) {
            $table->string('corpus_api_key', 500)->nullable()->change();
        });
        Schema::table('agent_tokens', function (Blueprint $table) {
            $table->string('token', 500)->change();
        });
    }
};
