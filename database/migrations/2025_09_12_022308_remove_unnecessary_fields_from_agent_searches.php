<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('agent_searches', function (Blueprint $table) {
            $table->dropColumn('query');
            $table->dropColumn('filters');
            $table->dropColumn('results');
            $table->dropConstrainedForeignId('agent_id');
            $table->dropConstrainedForeignId('agent_token_id');
            $table->dropConstrainedForeignId('agent_prompt_id');
            $table->unsignedBigInteger('agent_id')->after('id');
            $table->unsignedBigInteger('agent_token_id')->nullable()->after('agent_id');
            $table->unsignedBigInteger('agent_prompt_id')->nullable()->after('agent_token_id');
            $table->boolean('is_billed')->default(false)->after('searched_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('agent_searches', function (Blueprint $table) {
            $table->dropColumn('is_billed');
        });
    }
};
