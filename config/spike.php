<?php

use Opcodes\Spike\CreditAmount;

return [

    /*
    |--------------------------------------------------------------------------
    | Path to the Spike billing portal
    |--------------------------------------------------------------------------
    |
    | Here you can configure the path which the Spike billing portal
    | will be accessible on. For example, the value of "billing"
    | will make the billing portal accessible via "/billing".
    | Setting this to `null` will disable the billing portal.
    |
    */

    'path' => 'billing',

    /*
    |--------------------------------------------------------------------------
    | Spike middleware
    |--------------------------------------------------------------------------
    |
    | Here you can configure the middleware that will be applied to all
    | routes of the Spike billing portal. By default, Spike will
    | only be accessible by authenticated users.
    |
    */

    'middleware' => [
        'web',
        'auth',
    ],

    /*
    |--------------------------------------------------------------------------
    | Spike Theme
    |--------------------------------------------------------------------------
    |
    | Here you can configure the color and the logo of the Spike billing
    | portal. You should prefer dark theme colors. If you would like
    | the logo to be hidden, you can set the value to `null`.
    |
    */

    'theme' => [
        'color' => env('AGENT_DEFAULT_PRIMARY_COLOR'),
        'logo_url' => '/img/logo-light.svg',

        // URL/path to the favicon (small logo in browser tab).
        'favicon_url' => '/img/favicon.svg',

        // If you would like to change how the avatar is resolved,
        // use Spike::resolveAvatarUsing($callback) in your AppServiceProvider
        'display_avatar' => true,

        // Whether to display zero values for credit types that the user did not yet purchase.
        'display_empty_credit_balances' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Return to your app URL
    |--------------------------------------------------------------------------
    |
    | Here you can configure which URL the user can return to after
    | they are finished with the billing. If you would like the
    | link to be hidden, you can set this value to `null`.
    |
    */

    'return_url' => '/',

    /*
    |--------------------------------------------------------------------------
    | Billable models
    |--------------------------------------------------------------------------
    |
    | Here you should add all the different billable models you have in
    | your app that have subscriptions. These models will be used to
    | distribute monthly credits to active subscribers.
    |
    */

    'billable_models' => [
        'App\Models\Team',
    ],

    /*
    |--------------------------------------------------------------------------
    | Credit types
    |--------------------------------------------------------------------------
    |
    | You can have multiple types of credits that you can sell and use,
    | like API credits, SMS, emails, etc. Here you can configure the
    | different credit types that will be available for users.
    |
    */

    'credit_types' => [
        [

            // ID is used when referencing the credit type in the code, i.e. `$user->credits('sms')->balance()`.
            'id' => 'credits',

            // The translation key with inflection to use for this credit type.
            'translation_key' => 'spike::translations.credits',

            // The icon for credit type. Leaving this `null` will use the default icon (coins).
            // Accepted values are: URL string (absolute or relative), SVG string, or null.
            'icon' => null,

            // When a credit type is allowed to have a negative balance, Spike will automatically charge for
            // negative balances every month. This price should represent a single credit of this type.
            'payment_provider_price_id' => env('SPIKE_PROVIDER_PRICE_ID_1_CREDIT'),

            'charge_negative_balances' => true,

        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Credit spend grouping
    |--------------------------------------------------------------------------
    |
    | By default, credit usage information is grouped daily. This means that
    | if a user makes 100 requests in a day, there will only be a single
    | usage record for that day. This is useful for performance, but
    | does not allow adding notes to each individual usage record.
    |
    */

    'group_credit_spend_daily' => true,

    /*
    |--------------------------------------------------------------------------
    | Subscriptions
    |--------------------------------------------------------------------------
    |
    | Here you can configure the different subscription plans to be
    | available for your users. Below are just some examples and
    | you should change these values for your own. You can
    | learn more about configuring subscriptions here:
    |
    | https://spike.opcodes.io/docs/3.x/configuring-spike/subscriptions
    |
    */

    'subscriptions' => [

        [
            'id' => 'free',
            'name' => 'Free',
            'short_description' => 'Small ministries & hobbyists',
            'features' => [
                'Use our standard Agent',
                'Default corpus',
                'API access',
            ],
            'provides_monthly' => [
                CreditAmount::make(1_000)->expiresAfterMonths(1),
            ],
            'options' => [
                'users' => 1,
                'support_tier' => 1,
                'automated_ingestion' => false,
                'bulk_import' => false,
                'analytics' => false,
                'agent' => [
                    'included_agents' => 0,
                    'included_credits' => 1_000,
                    'white_label' => false,
                    'instructions' => false,
                    'custom_sources' => false,
                    'model_config' => false,
                    'model_categories' => ['limited', 'standard', 'premium', 'reasoning'],
                    'advanced_config' => false,
                    'custom_questions' => false,
                    'integrations' => false,
                    'api' => false,
                    'realtime_translation' => false,
                    'voice' => false,
                    'prompts_download' => true,
                    'media' => false,
                    'password_protection' => false,
                    'semantic_search' => false,
                ],
                'social' => [
                    'contributor_profiles' => true,
                    'organization_profile' => true,
                    'featured_placement' => false,
                ],
            ],
        ],

        [
            'id' => 'basic',
            'name' => 'Basic',
            'short_description' => 'Larger ministries & churches',
            'payment_provider_price_id_monthly' => env('SPIKE_PROVIDER_PRICE_ID_BASIC_MONTHLY'),
            'payment_provider_price_id_yearly' => env('SPIKE_PROVIDER_PRICE_ID_BASIC_YEARLY'),
            'price_in_cents_monthly' => 99_00,
            'price_in_cents_yearly' => 990_00,
            'features' => [
                'Everything in Free',
                'Configure your own Agent',
                'Comprehensive white-labeling options',
            ],
            'provides_monthly' => [
                CreditAmount::make(5_000)->expiresAfterMonths(1),
            ],
            'options' => [
                'users' => 999,
                'support_tier' => 2,
                'automated_ingestion' => true,
                'bulk_import' => true,
                'analytics' => false,
                'agent' => [
                    'included_agents' => 1,
                    'included_credits' => 5_000,
                    'white_label' => false,
                    'instructions' => true,
                    'custom_sources' => false,
                    'model_config' => true,
                    'model_categories' => ['limited', 'standard', 'premium', 'reasoning'],
                    'advanced_config' => true,
                    'custom_questions' => true,
                    'integrations' => true,
                    'api' => true,
                    'realtime_translation' => true,
                    'voice' => true,
                    'prompts_download' => false,
                    'media' => false,
                    'password_protection' => true,
                    'semantic_search' => true,
                ],
                'social' => [
                    'contributor_profiles' => true,
                    'organization_profile' => true,
                    'featured_placement' => false,
                ],
            ],
        ],

        [
            'id' => 'pro',
            'name' => 'Pro',
            'short_description' => 'International ministries & megachurches',
            'payment_provider_price_id_monthly' => env('SPIKE_PROVIDER_PRICE_ID_PRO_MONTHLY'),
            'payment_provider_price_id_yearly' => env('SPIKE_PROVIDER_PRICE_ID_PRO_YEARLY'),
            'price_in_cents_monthly' => 249_00,
            'price_in_cents_yearly' => 2490_00,
            'features' => [
                'Everything in Basic',
                'Unlimited custom Agents',
                'Social & messaging platform integrations',
            ],
            'provides_monthly' => [
                CreditAmount::make(20_000)->expiresAfterMonths(1),
            ],
            'options' => [
                'users' => 999,
                'support_tier' => 3,
                'automated_ingestion' => true,
                'bulk_import' => true,
                'analytics' => true,
                'agent' => [
                    'included_agents' => 10,
                    'included_credits' => 20_000,
                    'white_label' => true,
                    'instructions' => true,
                    'custom_sources' => true,
                    'model_config' => true,
                    'model_categories' => ['limited', 'standard', 'premium', 'reasoning'],
                    'advanced_config' => true,
                    'custom_questions' => true,
                    'integrations' => true,
                    'api' => true,
                    'realtime_translation' => true,
                    'voice' => true,
                    'prompts_download' => true,
                    'media' => true,
                    'password_protection' => true,
                    'semantic_search' => true,
                ],
                'social' => [
                    'contributor_profiles' => true,
                    'organization_profile' => true,
                    'featured_placement' => true,
                ],
            ],
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Products
    |--------------------------------------------------------------------------
    |
    | Here you can configure the different products (one-off purchases)
    | to be available for your users. Below are just some examples
    | and you should change these values for your own. You can
    | learn more about configuring products here:
    |
    | https://spike.opcodes.io/docs/3.x/configuring-spike/products
    |
    */

    'products' => [

        [
            'id' => '5k_credits',
            'name' => '5,000 Credits',
            'short_description' => null,
            'payment_provider_price_id' => env('SPIKE_PROVIDER_PRICE_ID_5K_CREDITS'),
            'price_in_cents' => 25_00,
            'features' => [],
            'provides' => [
                CreditAmount::make(5_000),
            ],
        ],

        [
            'id' => '20k_credits',
            'name' => '20,000 Credits',
            'short_description' => '5% off',
            'payment_provider_price_id' => env('SPIKE_PROVIDER_PRICE_ID_20K_CREDITS'),
            'price_in_cents' => 95_00,
            'features' => [],
            'provides' => [
                CreditAmount::make(20_000),
            ],
        ],

        [
            'id' => '50k_credits',
            'name' => '50,000 Credits',
            'short_description' => '10% off',
            'payment_provider_price_id' => env('SPIKE_PROVIDER_PRICE_ID_50K_CREDITS'),
            'price_in_cents' => 225_00,
            'features' => [],
            'provides' => [
                CreditAmount::make(50_000),
            ],
        ],

        [
            'id' => '100k_credits',
            'name' => '100,000 Credits',
            'short_description' => '25% off',
            'payment_provider_price_id' => env('SPIKE_PROVIDER_PRICE_ID_100K_CREDITS'),
            'price_in_cents' => 375_00,
            'features' => [],
            'provides' => [
                CreditAmount::make(100_000),
            ],
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Date formats
    |--------------------------------------------------------------------------
    |
    | Here you can configure the date format patterns used across the application.
    | These formats use PHP date format strings and will be used with Carbon's
    | translatedFormat method for localization support.
    |
    */

    'date_formats' => [
        // Credit transaction formats
        'transaction_date' => 'F j, Y',               // Credit transaction date with year
        'transaction_date_current_year' => 'F j',     // Credit transaction date in current year
        'transaction_expiry_date' => 'F j, Y',        // Credit expiration date with year
        'transaction_expiry_current_year' => 'F j',   // Credit expiration date in current year
        'transaction_time' => 'g:i a',                // Time format for usage transactions
        'transaction_usage_datetime' => 'F j, H:i:s', // Usage date with time in current year
        'transaction_usage_with_year' => 'F j Y, H:i:s', // Usage date with time for previous years

        // Invoice formats
        'invoice_date' => 'F j, Y',                   // Date format on invoices

        // Subscription formats
        'subscription_end_date' => 'F j, Y',          // Subscription end date

        // Payment method formats
        'payment_method_expiry' => 'F, Y',            // Payment method expiry date
    ],

    /*
    |--------------------------------------------------------------------------
    | Stripe payment gateway configuration
    |--------------------------------------------------------------------------
    |
    | This section is used to configure the Stripe payment gateway only.
    | https://spike.opcodes.io/docs/3.x/payment-providers/stripe
    |
    */

    'stripe' => [

        /*
        |--------------------------------------------------------------------------
        | Use Stripe Checkout instead of the in-built UI
        |--------------------------------------------------------------------------
        |
        | When disabled, Spike will use the in-built checkout UI for both
        | products and subscriptions. If you would rather use
        | Stripe's Checkout UI, you can enable this.
        |
        */

        'checkout' => [
            'enabled' => false,

            // Whether to generate invoices for product purchases (one-time payments) made using Stripe Checkout.
            // Post-checkout invoices for one-time payments are not free. Please see the Stripe support article below:
            // https://support.stripe.com/questions/pricing-for-post-payment-invoices-for-one-time-purchases-via-checkout-and-payment-links
            'generate_invoices_for_products' => false,

            // For support Stripe Checkout locales, please see this link:
            // https://docs.stripe.com/api/checkout/sessions/create#create_checkout_session-locale
            'default_locale' => null,

            'cancellation_offers' => [
                new \Opcodes\Spike\Stripe\CouponCodeOffer(
                    couponId: 'cancellation_discount',
                    name: '25% off for 3 months',
                    description: 'Stay with us and get 25% off your subscription for the next 3 months.'
                ),
            ],

        ],

        /*
        |--------------------------------------------------------------------------
        | Invoice details
        |--------------------------------------------------------------------------
        |
        | Here you can configure the details of the invoices downloadable
        | by your users. Invoices are created automatically for every
        | subscription and product purchase. The details below
        | will be visible on every invoice downloaded.
        |
        | https://spike.opcodes.io/docs/3.x/payment-providers/stripe#invoice-details
        |
        */

        'invoice_details' => [
            'vendor' => env('ORG_NAME', 'Spike'),
            'product' => env('SPIKE_INVOICE_PRODUCT', 'Spike'),
             'street' => env('SPIKE_INVOICE_ADDRESS', '123 Fake Street'),
             'location' => env('SPIKE_INVOICE_LOCATION', 'Fake City, FC 12345'),
//             'phone' => '+370 646 00 000',
             'email' => env('SPIKE_INVOICE_EMAIL', '<EMAIL>'),
             'url' => env('SPIKE_INVOICE_URL', 'https://test.com'),
//             'vendorVat' => 'LT123456789',
        ],

        /*
        |--------------------------------------------------------------------------
        | Allow users to enter discount codes
        |--------------------------------------------------------------------------
        |
        | Configure whether users should be able to enter discount codes.
        | Discount codes can be configured in the Stripe dashboard.
        |
        | https://spike.opcodes.io/docs/3.x/payment-providers/stripe#using-coupons
        |
        */

        'allow_discount_codes' => true,

        /*
        |--------------------------------------------------------------------------
        | Persist applied discounts when switching plans
        |--------------------------------------------------------------------------
        |
        | When enabled, the previously applied discount code will carry over
        | to the new plan when switching plans. When disabled, the discount
        | will be removed when switching plans.
        |
        */

        'persist_discounts_when_switching_plans' => true,

        /*
        |--------------------------------------------------------------------------
        | Allow incomplete subscription updates
        |--------------------------------------------------------------------------
        |
        | When enabled, subscription plan changes will proceed even if the payment
        | for price difference fails. This may result in subscriptions going into
        | a "past_due" state that may be considered inactive. When disabled,
        | plan changes will fail completely if payment cannot be completed.
        |
        */

        'allow_incomplete_subscription_updates' => false,
    ],

];
