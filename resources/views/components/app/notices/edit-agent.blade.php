<div x-data="{ apgHideAgentNotice: $persist(false).as('apgHideAgentNotice') }">
    <x-shout::shout
        type="info"
        color="primary"
        icon="heroicon-s-information-circle"
        x-show="!apgHideAgentNotice"
    >

        <p>
            <strong>Agent management has been rearranged to make it easier to find what you're looking for.</strong>
            Use the Agent navigation bar below to access Agent management categories, then navigate fields in each category using the form tabs.
        </p>

        <div class="text-right">
            <x-filament::button
                x-on:click="apgHideAgentNotice = true"
                icon="heroicon-o-x-mark"
                color="primary"
                class="mt-4"
                size="sm"
            >
                Dismiss
            </x-filament::button>
        </div>

    </x-shout::shout>
</div>

